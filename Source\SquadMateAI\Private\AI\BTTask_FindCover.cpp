#include "AI/BTTask_FindCover.h"
#include "AI/TDMAIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "EnvironmentQuery/EnvQuery.h"
#include "EnvironmentQuery/EnvQueryManager.h"
#include "AIController.h"
#include "NavigationSystem.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"

UBTTask_FindCover::UBTTask_FindCover()
{
    NodeName = "Find Cover (EQS)";
    bNotifyTick = false;
    bNotifyTaskFinished = true;

    // Initialize default blackboard keys
    TargetActorKey.SelectedKeyName = "TargetActor";
    CoverLocationKey.SelectedKeyName = "CoverLocation";
    IsUnderFireKey.SelectedKeyName = "IsUnderFire";
    SquadMembersKey.SelectedKeyName = "SquadMembers";

    // Set default emergency parameters for faster response
    EmergencyParams.MaxSearchRadius = 800.0f;
    EmergencyParams.MinSearchRadius = 100.0f;
    EmergencyParams.MinDistanceFromEnemy = 150.0f;
    EmergencyParams.bRequireLineOfSight = false;
}

EBTNodeResult::Type UBTTask_FindCover::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    APawn* ControlledPawn = AIController ? AIController->GetPawn() : nullptr;

    if (!AIController || !ControlledPawn)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindCover: Invalid AI Controller or Pawn"));
        return EBTNodeResult::Failed;
    }

    // Cache the owner component for async callback
    CachedOwnerComp = &OwnerComp;

    // Start the EQS query
    StartCoverQuery(OwnerComp);

    return EBTNodeResult::InProgress;
}

void UBTTask_FindCover::StartCoverQuery(UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    APawn* ControlledPawn = AIController->GetPawn();

    // Select the appropriate query based on situation
    UEnvQuery* QueryToUse = SelectOptimalQuery(OwnerComp);
    if (!QueryToUse)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindCover: No valid EQS query found"));
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Create and configure the query request
    FEnvQueryRequest QueryRequest(QueryToUse, ControlledPawn);
    ConfigureQueryParams(QueryRequest, OwnerComp);

    // Execute the query
    ActiveQueryRequest = QueryRequest;
    QueryRequest.Execute(EEnvQueryRunMode::SingleResult, AIController,
        FQueryFinishedSignature::CreateUObject(this, &UBTTask_FindCover::OnEQSQueryComplete, &OwnerComp));
}

UEnvQuery* UBTTask_FindCover::SelectOptimalQuery(UBehaviorTreeComponent& OwnerComp)
{
    // Use emergency query if under fire and available
    if (bUseEmergencyQueryWhenUnderFire && IsUnderFire(OwnerComp) && EmergencyCoverQuery)
    {
        return EmergencyCoverQuery;
    }

    // Default to standard cover query
    return CoverQuery;
}

void UBTTask_FindCover::ConfigureQueryParams(FEnvQueryRequest& QueryRequest, UBehaviorTreeComponent& OwnerComp)
{
    FCoverSearchParams CurrentParams = GetAdjustedParams(OwnerComp);
    AActor* CurrentTarget = GetCurrentTarget(OwnerComp);

    // Set query parameters
    QueryRequest.SetFloatParam("MaxSearchRadius", CurrentParams.MaxSearchRadius);
    QueryRequest.SetFloatParam("MinSearchRadius", CurrentParams.MinSearchRadius);
    QueryRequest.SetFloatParam("MinCoverHeight", CurrentParams.MinCoverHeight);
    QueryRequest.SetFloatParam("MinDistanceFromEnemy", CurrentParams.MinDistanceFromEnemy);
    QueryRequest.SetFloatParam("MaxDistanceFromEnemy", CurrentParams.MaxDistanceFromEnemy);
    QueryRequest.SetFloatParam("SquadCohesionRadius", CurrentParams.SquadCohesionRadius);

    // Set context actors
    if (CurrentTarget)
    {
        QueryRequest.SetActorParam("EnemyTarget", CurrentTarget);
    }

    TArray<AActor*> SquadMembers = GetSquadMembers(OwnerComp);
    if (SquadMembers.Num() > 0)
    {
        QueryRequest.SetActorsParam("SquadMembers", SquadMembers);
    }
}

FCoverSearchParams UBTTask_FindCover::GetAdjustedParams(UBehaviorTreeComponent& OwnerComp)
{
    // Use emergency parameters if under fire
    if (IsUnderFire(OwnerComp))
    {
        return EmergencyParams;
    }

    return CoverParams;
}

void UBTTask_FindCover::OnEQSQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp)
{
    if (!OwnerComp || !Result.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindCover: Invalid query result or owner component"));
        if (OwnerComp)
        {
            FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
        }
        return;
    }

    if (Result->IsSuccessful() && Result->Items.Num() > 0)
    {
        // Get the best cover location
        FVector CoverLocation = Result->GetItemAsLocation(0);
        
        // Validate the position
        if (ValidateCoverPosition(CoverLocation, *OwnerComp))
        {
            // Store in blackboard
            UBlackboardComponent* BlackboardComp = OwnerComp->GetBlackboardComponent();
            if (BlackboardComp)
            {
                BlackboardComp->SetValueAsVector(CoverLocationKey.SelectedKeyName, CoverLocation);
                
                // Debug visualization
                if (AAIController* AIController = OwnerComp->GetAIOwner())
                {
                    DrawDebugSphere(AIController->GetWorld(), CoverLocation, 100.0f, 12, FColor::Green, false, 5.0f);
                }
            }

            // Initiate movement if configured
            if (bMoveToFoundCover)
            {
                InitiateMovementToCover(CoverLocation, *OwnerComp);
            }

            FinishLatentTask(*OwnerComp, EBTNodeResult::Succeeded);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("BTTask_FindCover: Cover position validation failed"));
            FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindCover: EQS query failed to find valid cover"));
        FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
    }
}

bool UBTTask_FindCover::IsUnderFire(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        return BlackboardComp->GetValueAsBool(IsUnderFireKey.SelectedKeyName);
    }
    return false;
}

AActor* UBTTask_FindCover::GetCurrentTarget(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        return Cast<AActor>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
    }
    return nullptr;
}

TArray<AActor*> UBTTask_FindCover::GetSquadMembers(UBehaviorTreeComponent& OwnerComp)
{
    TArray<AActor*> SquadMembers;
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        // Implementation depends on how squad members are stored in blackboard
        // This is a placeholder - adjust based on your squad system
    }
    return SquadMembers;
}

bool UBTTask_FindCover::ValidateCoverPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController || !AIController->GetPawn())
    {
        return false;
    }

    // Check if position is reachable via navigation
    UNavigationSystemV1* NavSys = UNavigationSystemV1::GetCurrent(AIController->GetWorld());
    if (NavSys)
    {
        FNavLocation NavLocation;
        if (!NavSys->ProjectPointToNavigation(Position, NavLocation, FVector(100.0f, 100.0f, 100.0f)))
        {
            return false;
        }
    }

    return true;
}

void UBTTask_FindCover::InitiateMovementToCover(const FVector& CoverLocation, UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (AIController && bUsePathfinding)
    {
        AIController->MoveToLocation(CoverLocation, AcceptableRadius);
    }
}

void UBTTask_FindCover::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
    // Clean up any active queries
    ActiveQueryRequest = FEnvQueryRequest();
    CachedOwnerComp.Reset();
    
    Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_FindCover::GetStaticDescription() const
{
    return FString::Printf(TEXT("Find Cover using EQS\nQuery: %s\nEmergency Query: %s"), 
        CoverQuery ? *CoverQuery->GetName() : TEXT("None"),
        EmergencyCoverQuery ? *EmergencyCoverQuery->GetName() : TEXT("None"));
}
