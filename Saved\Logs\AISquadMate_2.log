﻿Log file open, 06/14/25 09:36:12
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=11280)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AISquadMate
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.2604] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|Intel(R) Core(TM) i7-7700HQ CPU @ 2.80GHz"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:\AISquadmate\AISquadMate.uproject""
LogCsvProfiler: Display: Metadata set : loginid="4a1d1b0945561a4090f9c5b988d40ef4"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.409984
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-B2CF5747451D24DF1E6D74A3971F4E30
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../AISquadmate/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../AISquadmate/Binaries/Win64/*.target
LogAssetRegistry: Display: No AssetDiscovery cache present at ../../../../../../AISquadmate/Intermediate/CachedAssetRegistryDiscovery.bin. AssetRegistry discovery of files will be uncached.
LogConfig: Display: Loading IOS ini files took 0.07 seconds
LogConfig: Display: Loading Mac ini files took 0.08 seconds
LogConfig: Display: Loading Android ini files took 0.09 seconds
LogConfig: Display: Loading Linux ini files took 0.11 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.07 seconds
LogConfig: Display: Loading Unix ini files took 0.09 seconds
LogConfig: Display: Loading TVOS ini files took 0.10 seconds
LogConfig: Display: Loading Windows ini files took 0.09 seconds
LogConfig: Display: Loading VisionOS ini files took 0.08 seconds
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../AISquadmate/Binaries/Win64/*.target
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=4a1d1b0945561a4090f9c5b988d40ef4
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.2604] (), CPU: Intel(R) Core(TM) i7-7700HQ CPU @ 2.80GHz, GPU: Intel(R) HD Graphics 630
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.14-04.06.13:213][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.14-04.06.13:213][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.14-04.06.13:213][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.14-04.06.13:214][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.14-04.06.13:214][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.14-04.06.13:214][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.14-04.06.13:217][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.06.14-04.06.13:217][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.06.14-04.06.13:217][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.14-04.06.13:217][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.14-04.06.13:217][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.14-04.06.13:218][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.06.14-04.06.13:218][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.14-04.06.13:219][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.14-04.06.13:219][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.14-04.06.13:219][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.14-04.06.13:220][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.14-04.06.13:220][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.14-04.06.13:220][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.14-04.06.13:312][  0]LogRHI: Using Default RHI: D3D12
[2025.06.14-04.06.13:312][  0]LogRHI: Using Highest Feature Level of D3D12: SM5
[2025.06.14-04.06.13:312][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.14-04.06.13:318][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.14-04.06.13:318][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM5 is supported by your system.
[2025.06.14-04.06.14:765][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce GTX 1050 Ti (VendorId: 10de, DeviceId: 1c8c, SubSysId: 8259103c, Revision: 00a1
[2025.06.14-04.06.14:765][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.14-04.06.14:765][  0]LogD3D12RHI:   Adapter has 4004MB of dedicated video memory, 0MB of dedicated system memory, and 8117MB of shared system memory, 1 output[s], UMA:false
[2025.06.14-04.06.14:766][  0]LogD3D12RHI:   Driver Version: 572.83 (internal:32.0.15.7283, unified:572.83)
[2025.06.14-04.06.14:766][  0]LogD3D12RHI:      Driver Date: 3-14-2025
[2025.06.14-04.06.14:855][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.06.14-04.06.14:861][  0]LogD3D12RHI: Found D3D12 adapter 1: Intel(R) HD Graphics 630 (VendorId: 8086, DeviceId: 591b, SubSysId: 8259103c, Revision: 0004
[2025.06.14-04.06.14:861][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.5, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.14-04.06.14:861][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 8117MB of shared system memory, 0 output[s], UMA:true
[2025.06.14-04.06.14:861][  0]LogD3D12RHI:   Driver Version: 26.20.100.7528 (internal:26.20.100.7528, unified:100.7528)
[2025.06.14-04.06.14:861][  0]LogD3D12RHI:      Driver Date: 11-13-2019
[2025.06.14-04.06.14:869][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.14-04.06.14:869][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.14-04.06.14:869][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 8117MB of shared system memory, 0 output[s], UMA:true
[2025.06.14-04.06.14:869][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.14-04.06.14:869][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.14-04.06.14:869][  0]LogRHI: RHI D3D12 with Feature Level SM5 is supported and will be used.
[2025.06.14-04.06.14:869][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.14-04.06.14:869][  0]LogHAL: Display: Platform has ~ 16 GB [17024389120 / 17179869184 / 16], which maps to Larger [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.14-04.06.14:870][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.14-04.06.14:870][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.14-04.06.14:870][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.14-04.06.14:870][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.14-04.06.14:870][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.14-04.06.14:871][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.14-04.06.14:871][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.14-04.06.14:871][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.14-04.06.14:871][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.14-04.06.14:871][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.14-04.06.14:871][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.14-04.06.14:871][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.14-04.06.14:871][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/AISquadmate/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.14-04.06.14:871][  0]LogInit: Computer: DESKTOP-9I4BNV2
[2025.06.14-04.06.14:871][  0]LogInit: User: BCE
[2025.06.14-04.06.14:871][  0]LogInit: CPU Page size=4096, Cores=4
[2025.06.14-04.06.14:871][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.14-04.06.14:871][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.06.14-04.06.14:871][  0]LogMemory: Memory total: Physical=15.9GB (16GB approx) Virtual=21.2GB
[2025.06.14-04.06.14:871][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.14-04.06.14:871][  0]LogMemory: Process Physical Memory: 562.63 MB used, 578.28 MB peak
[2025.06.14-04.06.14:871][  0]LogMemory: Process Virtual Memory: 534.14 MB used, 537.53 MB peak
[2025.06.14-04.06.14:871][  0]LogMemory: Physical Memory: 10840.32 MB used,  5395.40 MB free, 16235.72 MB total
[2025.06.14-04.06.14:871][  0]LogMemory: Virtual Memory: 16770.79 MB used,  4907.49 MB free, 21678.28 MB total
[2025.06.14-04.06.14:871][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.14-04.06.14:874][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.14-04.06.14:879][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.14-04.06.14:879][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.14-04.06.14:880][  0]LogInit: Using OS detected language (en-US).
[2025.06.14-04.06.14:880][  0]LogInit: Using OS detected locale (en-US).
[2025.06.14-04.06.14:892][  0]LogTextLocalizationManager: No specific localization for 'en-US' exists, so 'en' will be used for the language.
[2025.06.14-04.06.14:892][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.14-04.06.15:459][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.14-04.06.15:459][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.14-04.06.15:459][  0]LogWindowsTextInputMethodSystem:   - English (India) - (Keyboard).
[2025.06.14-04.06.15:459][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.14-04.06.15:465][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.06.14-04.06.15:470][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.06.14-04.06.15:480][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.14-04.06.15:480][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.14-04.06.15:751][  0]LogRHI: Using Default RHI: D3D12
[2025.06.14-04.06.15:751][  0]LogRHI: Using Highest Feature Level of D3D12: SM5
[2025.06.14-04.06.15:751][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.14-04.06.15:751][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM5 is supported by your system.
[2025.06.14-04.06.15:751][  0]LogRHI: RHI D3D12 with Feature Level SM5 is supported and will be used.
[2025.06.14-04.06.15:751][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.06.14-04.06.15:751][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM5
[2025.06.14-04.06.15:752][  0]LogWindows: Attached monitors:
[2025.06.14-04.06.15:752][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1030), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.14-04.06.15:752][  0]LogWindows: Found 1 attached monitors.
[2025.06.14-04.06.15:752][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.14-04.06.15:752][  0]LogRHI: RHI Adapter Info:
[2025.06.14-04.06.15:752][  0]LogRHI:             Name: NVIDIA GeForce GTX 1050 Ti
[2025.06.14-04.06.15:752][  0]LogRHI:   Driver Version: 572.83 (internal:32.0.15.7283, unified:572.83)
[2025.06.14-04.06.15:753][  0]LogRHI:      Driver Date: 3-14-2025
[2025.06.14-04.06.15:753][  0]LogD3D12RHI:     GPU DeviceId: 0x1c8c (for the marketing name, search the web for "GPU Device Id")
[2025.06.14-04.06.15:753][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.14-04.06.15:986][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.14-04.06.15:987][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.14-04.06.16:064][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.06.14-04.06.16:064][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: Raster order views are supported
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.14-04.06.16:064][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.14-04.06.16:168][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000021D052DE2C0)
[2025.06.14-04.06.16:169][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000021D052DE580)
[2025.06.14-04.06.16:169][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000021D052DE840)
[2025.06.14-04.06.16:169][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.14-04.06.16:169][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.14-04.06.16:416][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.06.14-04.06.16:416][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.06.14-04.06.16:416][  0]LogRHI: Texture pool is 2144 MB (70% of 3063 MB)
[2025.06.14-04.06.16:416][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.14-04.06.16:416][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.14-04.06.16:428][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.06.14-04.06.16:431][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.06.14-04.06.16:431][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.06.14-04.06.16:431][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.06.14-04.06.16:431][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.06.14-04.06.16:431][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.14-04.06.16:434][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/AISquadmate/AISquadMate.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/AISquadmate/Intermediate/TurnkeyReport_0.log" -log="C:/AISquadmate/Intermediate/TurnkeyLog_0.log" -project="C:/AISquadmate/AISquadMate.uproject"  -platform=all'
[2025.06.14-04.06.16:434][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/AISquadmate/AISquadMate.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/AISquadmate/Intermediate/TurnkeyReport_0.log" -log="C:/AISquadmate/Intermediate/TurnkeyLog_0.log" -project="C:/AISquadmate/AISquadMate.uproject"  -platform=all" ]
[2025.06.14-04.06.16:458][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.06.14-04.06.16:458][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.14-04.06.16:458][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.14-04.06.16:458][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.14-04.06.16:458][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.14-04.06.16:458][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.14-04.06.16:458][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.06.14-04.06.16:458][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.06.14-04.06.16:459][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.14-04.06.16:461][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.14-04.06.16:523][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.14-04.06.16:551][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.14-04.06.16:551][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.14-04.06.16:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.14-04.06.16:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.14-04.06.16:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.14-04.06.16:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.14-04.06.16:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.14-04.06.16:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.14-04.06.16:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.14-04.06.16:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.14-04.06.16:634][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.14-04.06.16:634][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.14-04.06.16:673][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.14-04.06.16:674][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.14-04.06.16:674][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.14-04.06.16:674][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.14-04.06.16:674][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.14-04.06.16:723][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.14-04.06.16:735][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.14-04.06.16:735][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.06.14-04.06.16:739][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.14-04.06.16:739][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../AISquadmate/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.14-04.06.16:739][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.14-04.06.16:739][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../AISquadmate/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.14-04.06.16:740][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.14-04.06.17:054][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.06.14-04.06.17:054][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.14-04.06.17:054][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.14-04.06.17:055][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.14-04.06.17:056][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.14-04.06.17:057][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.14-04.06.17:057][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.14-04.06.17:057][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 960  --child-id Zen_960_Startup'
[2025.06.14-04.06.17:247][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.14-04.06.17:247][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.191 seconds
[2025.06.14-04.06.17:249][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.14-04.06.17:278][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=556.43MBs, RandomWriteSpeed=88.10MBs. Assigned SpeedClass 'Local'
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.14-04.06.17:282][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.14-04.06.17:282][  0]LogShaderCompilers: Guid format shader working directory is 16 characters bigger than the processId version (../../../../../../AISquadmate/Intermediate/Shaders/WorkingDirectory/960/).
[2025.06.14-04.06.17:282][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/BB38F8474A11C869A493249765403FFA/'.
[2025.06.14-04.06.17:282][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.14-04.06.17:282][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.06.14-04.06.17:282][  0]LogShaderCompilers: Display: Using 5 local workers for shader compilation
[2025.06.14-04.06.17:282][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../AISquadmate/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.06.14-04.06.17:282][  0]LogShaderCompilers: Display: Failed to delete old shader autogen file: ../../../../../../AISquadmate/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.06.14-04.06.17:282][  0]LogShaderCompilers: Display: Shader autogen file written: ../../../../../../AISquadmate/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.06.14-04.06.18:519][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.14-04.06.20:248][  0]LogSlate: Using FreeType 2.10.0
[2025.06.14-04.06.20:250][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.14-04.06.20:256][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.14-04.06.20:256][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.14-04.06.20:256][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.14-04.06.20:256][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.14-04.06.20:283][  0]LogAssetRegistry: FAssetRegistry took 0.0027 seconds to start up
[2025.06.14-04.06.20:285][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.14-04.06.20:384][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../AISquadmate/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.14-04.06.20:902][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.14-04.06.20:902][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.14-04.06.21:000][  0]LogDeviceProfileManager: Active device profile: [0000021D3520B900][0000021D27730000 66] WindowsEditor
[2025.06.14-04.06.21:000][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.14-04.06.21:006][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.14-04.06.21:035][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.14-04.06.21:035][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.14-04.06.21:035][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.14-04.06.21:100][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.14-04.06.21:102][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/AISquadmate/AISquadMate.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/AISquadmate/Intermediate/TurnkeyReport_1.log" -log="C:/AISquadmate/Intermediate/TurnkeyLog_1.log" -project="C:/AISquadmate/AISquadMate.uproject"  -Device=Win64@DESKTOP-9I4BNV2'
[2025.06.14-04.06.21:102][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/AISquadmate/AISquadMate.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/AISquadmate/Intermediate/TurnkeyReport_1.log" -log="C:/AISquadmate/Intermediate/TurnkeyLog_1.log" -project="C:/AISquadmate/AISquadMate.uproject"  -Device=Win64@DESKTOP-9I4BNV2" -nocompile -nocompileuat ]
[2025.06.14-04.06.21:166][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:167][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.06.14-04.06.21:167][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:167][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:169][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.06.14-04.06.21:169][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:170][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:205][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.06.14-04.06.21:206][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.06.14-04.06.21:210][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.06.14-04.06.21:211][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:213][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:215][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:247][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.06.14-04.06.21:247][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.06.14-04.06.21:249][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.06.14-04.06.21:249][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:385][  0]LogMaterial: Display: Missing cached shadermap for DefaultPostProcessMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 0c58e2c3cbe69df91465df67454ca2bb677a566f), compiling. 
[2025.06.14-04.06.21:619][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.06.14-04.06.21:620][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:677][  0]LogMaterial: Display: Missing cached shadermap for DefaultLightFunctionMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: f6961d951caf86d834fccb235ec8c9673e39ea3c), compiling. 
[2025.06.14-04.06.21:945][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.06.14-04.06.21:945][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.21:991][  0]LogMaterial: Display: Missing cached shadermap for DefaultDeferredDecalMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 1ba8b8b285371f1e0e35d001edee6263290af942), compiling. 
[2025.06.14-04.06.23:365][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.06.14-04.06.23:365][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-04.06.23:629][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.14-04.06.23:702][  0]LogMaterial: Display: Missing cached shadermap for WorldGridMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: ca1043b370ef0296369956379ee19d00410d8fa1), compiling. Is special engine material.
[2025.06.14-04.06.31:335][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.14-04.06.31:335][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.14-04.06.31:335][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.14-04.06.31:335][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.14-04.06.31:335][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.14-04.06.33:589][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.06.14-04.06.33:694][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.14-04.06.33:694][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.14-04.06.33:694][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.06.14-04.06.33:695][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.06.14-04.06.33:701][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.14-04.06.33:702][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.14-04.06.33:703][  0]LogLiveCoding: Display: First instance in process group "UE_AISquadMate_0x6d543659", spawning console
[2025.06.14-04.06.33:712][  0]LogLiveCoding: Display: Waiting for server
[2025.06.14-04.06.33:741][  0]LogSlate: Border
[2025.06.14-04.06.33:741][  0]LogSlate: BreadcrumbButton
[2025.06.14-04.06.33:741][  0]LogSlate: Brushes.Title
[2025.06.14-04.06.33:741][  0]LogSlate: ColorPicker.ColorThemes
[2025.06.14-04.06.33:741][  0]LogSlate: Default
[2025.06.14-04.06.33:741][  0]LogSlate: Icons.Save
[2025.06.14-04.06.33:741][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.14-04.06.33:741][  0]LogSlate: ListView
[2025.06.14-04.06.33:741][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.14-04.06.33:741][  0]LogSlate: SoftwareCursor_Grab
[2025.06.14-04.06.33:741][  0]LogSlate: TableView.DarkRow
[2025.06.14-04.06.33:741][  0]LogSlate: TableView.Row
[2025.06.14-04.06.33:741][  0]LogSlate: TreeView
[2025.06.14-04.06.34:012][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.14-04.06.34:018][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 5.305 ms
[2025.06.14-04.06.34:061][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.14-04.06.34:061][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.14-04.06.34:061][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.14-04.06.34:099][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-9I4BNV2: (Name=DESKTOP-9I4BNV2, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.06.14-04.06.34:126][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.14-04.06.34:697][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.14-04.06.34:703][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.14-04.06.34:703][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.14-04.06.34:704][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:55391'.
[2025.06.14-04.06.34:712][  0]LogUdpMessaging: Display: Added local interface '192.168.1.8' to multicast group '230.0.0.1:6666'
[2025.06.14-04.06.34:720][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.06.14-04.06.35:011][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.14-04.06.35:012][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.14-04.06.35:066][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.14-04.06.35:536][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 8BDC5E08AF3C43B48000000000004300 | Instance: F9CBD28242961DB3A044B2B95ABE1495 (DESKTOP-9I4BNV2-960).
[2025.06.14-04.06.35:711][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.14-04.06.35:711][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: 0: Intel(R) HD Graphics 630 (Compute, Graphics)
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: 2: NVIDIA GeForce GTX 1050 Ti (Compute, Graphics)
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   NPU:       no
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT: Interface availability:
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   GPU: yes
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   RDG: yes
[2025.06.14-04.06.35:712][  0]LogNNERuntimeORT:   NPU: no
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: 0: Intel(R) HD Graphics 630 (Compute, Graphics)
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: 2: NVIDIA GeForce GTX 1050 Ti (Compute, Graphics)
[2025.06.14-04.06.35:743][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.14-04.06.35:803][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.06.14-04.06.35:803][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.06.14-04.06.35:819][  0]LogTimingProfiler: Initialize
[2025.06.14-04.06.35:819][  0]LogTimingProfiler: OnSessionChanged
[2025.06.14-04.06.35:819][  0]LoadingProfiler: Initialize
[2025.06.14-04.06.35:819][  0]LoadingProfiler: OnSessionChanged
[2025.06.14-04.06.35:819][  0]LogNetworkingProfiler: Initialize
[2025.06.14-04.06.35:819][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.14-04.06.35:819][  0]LogMemoryProfiler: Initialize
[2025.06.14-04.06.35:819][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.14-04.06.36:539][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.14-04.06.37:614][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.14-04.06.37:647][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.14-04.06.37:648][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.14-04.06.38:066][  0]SourceControl: Revision control is disabled
[2025.06.14-04.06.38:125][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.14-04.06.38:125][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.14-04.06.38:125][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.14-04.06.38:125][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.14-04.06.38:373][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.14-04.06.38:389][  0]SourceControl: Revision control is disabled
[2025.06.14-04.06.38:628][  0]LogCollectionManager: Loaded 0 collections in 0.018550 seconds
[2025.06.14-04.06.38:638][  0]LogFileCache: Scanning file cache for directory 'C:/AISquadmate/Saved/Collections/' took 0.01s
[2025.06.14-04.06.38:654][  0]LogFileCache: Scanning file cache for directory 'C:/AISquadmate/Content/Developers/BCE/Collections/' took 0.01s
[2025.06.14-04.06.38:668][  0]LogFileCache: Scanning file cache for directory 'C:/AISquadmate/Content/Collections/' took 0.01s
[2025.06.14-04.06.38:846][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.14-04.06.38:846][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.14-04.06.38:846][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.14-04.06.38:847][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.14-04.06.39:168][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.14-04.06.39:168][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.14-04.06.39:168][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.14-04.06.39:168][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.14-04.06.39:245][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-06-14T04:06:39.245Z using C
[2025.06.14-04.06.39:247][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.1741.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=AISquadMate, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.14-04.06.39:248][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.14-04.06.39:249][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.14-04.06.39:279][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.14-04.06.39:280][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.14-04.06.39:281][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.14-04.06.39:281][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000335
[2025.06.14-04.06.39:397][  0]LogUObjectArray: 42329 objects as part of root set at end of initial load.
[2025.06.14-04.06.39:397][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.14-04.06.39:634][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkInterface.LiveLinkSourceBufferManagementSettings. Time(ms): 2.4
[2025.06.14-04.06.39:634][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.ActorComponentInstanceSourceInfo. Time(ms): 3.2
[2025.06.14-04.06.39:751][  0]LogEngine: Initializing Engine...
[2025.06.14-04.06.40:049][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.14-04.06.40:050][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.06.14-04.06.41:241][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.14-04.06.41:403][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.14-04.06.41:473][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.14-04.06.41:496][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.06.14-04.06.41:496][  0]LogInit: Texture streaming: Enabled
[2025.06.14-04.06.41:509][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.0-43139311+++UE5+Release-5.6 )
[2025.06.14-04.06.41:513][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.14-04.06.41:519][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.14-04.06.41:520][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.14-04.06.41:522][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.14-04.06.41:522][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.14-04.06.41:522][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.14-04.06.41:522][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.14-04.06.41:522][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.14-04.06.41:522][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.14-04.06.41:522][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.14-04.06.41:534][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.14-04.06.42:243][  0]LogAudioMixer: Display: Using Audio Hardware Device Headphones (Probuds N41 Stereo)
[2025.06.14-04.06.42:244][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.14-04.06.42:248][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.14-04.06.42:248][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.14-04.06.42:250][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.14-04.06.42:250][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.14-04.06.42:253][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.14-04.06.42:253][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.14-04.06.42:253][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.14-04.06.42:253][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.14-04.06.42:254][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.14-04.06.42:266][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.14-04.06.42:280][  0]LogInit: Undo buffer set to 256 MB
[2025.06.14-04.06.42:280][  0]LogInit: Transaction tracking system initialized
[2025.06.14-04.06.42:379][  0]LocalizationService: Localization service is disabled
[2025.06.14-04.06.42:739][  0]LogFileCache: Scanning file cache for directory 'C:/AISquadmate/Content/' took 0.01s
[2025.06.14-04.06.42:780][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.06.14-04.06.42:870][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.06.14-04.06.42:870][  0]LogPython: Using Python 3.11.8
[2025.06.14-04.06.42:942][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.06.14-04.06.43:763][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.14-04.06.43:934][  0]LogEditorDataStorage: Initializing
[2025.06.14-04.06.43:940][  0]LogEditorDataStorage: Initialized
[2025.06.14-04.06.43:948][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.14-04.06.44:015][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.14-04.06.44:120][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.14-04.06.44:128][  0]SourceControl: Revision control is disabled
[2025.06.14-04.06.44:128][  0]LogUnrealEdMisc: Loading editor; pre map load, took 32.573
[2025.06.14-04.06.44:130][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.06.14-04.06.44:133][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.14-04.06.44:133][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-04.06.44:157][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.14-04.06.44:160][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.99ms
[2025.06.14-04.06.44:175][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.06.14-04.06.44:175][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.06.14-04.06.44:178][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.06.14-04.06.44:178][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.06.14-04.06.44:178][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.06.14-04.06.44:476][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.14-04.06.44:486][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.14-04.06.44:493][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.14-04.06.44:499][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.06.14-04.06.44:499][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.06.14-04.06.44:499][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.14-04.06.44:507][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.06.14-04.06.44:796][  0]LogWorldPartition: Display: WorldPartition initialize took 618.363 ms
[2025.06.14-04.06.45:213][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.14-04.06.45:238][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.15ms
[2025.06.14-04.06.45:239][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.14-04.06.45:242][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 2.675ms to complete.
[2025.06.14-04.06.45:259][  0]LogUnrealEdMisc: Total Editor Startup Time, took 33.704
[2025.06.14-04.06.45:312][  0]LogAssetRegistry: Display: Asset registry cache written as 66.6 MiB to ../../../../../../AISquadmate/Intermediate/CachedAssetRegistry_*.bin
[2025.06.14-04.06.45:385][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.06.14-04.06.45:663][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-04.06.45:798][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-04.06.45:910][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-04.06.46:024][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-04.06.46:235][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:247][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.14-04.06.46:249][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:258][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.14-04.06.46:258][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:267][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.14-04.06.46:267][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:277][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.14-04.06.46:278][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:287][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.14-04.06.46:288][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:298][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.14-04.06.46:298][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:309][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.14-04.06.46:309][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:323][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.14-04.06.46:323][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:333][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.14-04.06.46:334][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-04.06.46:346][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.14-04.06.46:785][  0]LogSlate: Took 0.000332 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.14-04.06.46:796][  0]LogSlate: Took 0.000795 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.14-04.06.46:797][  0]LogSlate: Took 0.000639 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.14-04.06.47:846][  0]LogPSOHitching: Encountered 50 PSO creation hitches so far (25 graphics, 25 compute). 0 of them were precached.
[2025.06.14-04.06.48:118][  0]LogStall: Startup...
[2025.06.14-04.06.48:122][  0]LogStall: Startup complete.
[2025.06.14-04.06.48:224][  0]LogEnhancedInputEditor: Upgrading Default Input Component class from 'InputComponent' to 'EnhancedInputComponent'
[2025.06.14-04.06.48:224][  0]LogEnhancedInputEditor: Upgrading Default Player Input class from 'PlayerInput' to 'EnhancedPlayerInput'
[2025.06.14-04.06.48:284][  0]LogLoad: (Engine Initialization) Total time: 36.73 seconds
[2025.06.14-04.06.49:328][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.14-04.06.49:329][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.14-04.06.49:451][  0]LogSlate: Took 0.000226 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.14-04.06.49:587][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-04.06.49:589][  0]LogPython: Display: Running start-up script C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.14-04.06.49:669][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.14-04.06.49:676][  0]LogPython: Display: Running start-up script C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 86.587 ms
[2025.06.14-04.06.50:147][  1]LogFileCache: Retrieving MD5 hashes for directory 'C:/AISquadmate/Content/' took 0.92s
[2025.06.14-04.06.50:149][  1]LogAssetRegistry: AssetRegistryGather time 23.1265s: AssetDataDiscovery 0.1625s, AssetDataGather 22.8492s, StoreResults 0.1148s. Wall time 29.8680s.
	NumCachedDirectories 0. NumUncachedDirectories 1460. NumCachedFiles 0. NumUncachedFiles 7376.
	BackgroundTickInterruptions 0.
[2025.06.14-04.06.50:192][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.06.14-04.06.50:204][  1]LogStreaming: Display: FlushAsyncLoading(404): 1 QueuedPackages, 0 AsyncPackages
[2025.06.14-04.06.50:209][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.06.14-04.06.50:209][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.06.14-04.06.50:734][  2]LogSourceControl: Uncontrolled asset discovery finished in 0.5253 seconds (Found 7352 uncontrolled assets)
[2025.06.14-04.06.51:348][  3]LogStreaming: Display: FlushAsyncLoading(410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473): 64 QueuedPackages, 0 AsyncPackages
[2025.06.14-04.06.51:655][  3]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 11.830301
[2025.06.14-04.06.51:656][  3]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.14-04.06.51:658][  3]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12.374006
[2025.06.14-04.06.51:860][  5]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.14-04.06.52:228][  7]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.14-04.06.52:581][ 13]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 13.232074
[2025.06.14-04.06.52:597][ 13]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.06.14-04.06.52:597][ 13]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13.232074, Update Interval: 344.799957
[2025.06.14-04.07.20:815][178]LogSlate: Window 'Plugins' being destroyed
[2025.06.14-04.07.20:822][178]LogSlate: Window 'Plugins' being destroyed
[2025.06.14-04.08.17:267][389]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.14-04.12.37:756][212]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 358.475677
[2025.06.14-04.12.38:756][215]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-04.12.38:756][215]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 359.142700, Update Interval: 348.182007
[2025.06.14-04.18.30:898][271]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 711.618530
[2025.06.14-04.18.31:898][274]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-04.18.31:898][274]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 712.284790, Update Interval: 311.735596
[2025.06.14-04.23.50:297][229]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1031.018555
[2025.06.14-04.23.51:296][232]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-04.23.51:296][232]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1031.685425, Update Interval: 303.570679
[2025.06.14-04.29.06:394][177]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1347.115112
[2025.06.14-04.29.07:395][180]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-04.29.07:395][180]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1347.781616, Update Interval: 304.931183
[2025.06.14-04.34.41:444][182]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1682.165405
[2025.06.14-04.34.42:445][185]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-04.34.42:445][185]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1682.832397, Update Interval: 356.240723
[2025.06.14-04.41.11:226][351]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2071.945557
[2025.06.14-04.41.12:225][354]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-04.41.12:225][354]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2072.616455, Update Interval: 319.515991
[2025.06.14-05.15.13:202][ 16]LogAudioMixer: Display: Attempt to swap audio render device to new device: '[System Default]', because: 'AudioMixerPlatformInterface. Timeout waiting for h/w.', force=1
[2025.06.14-05.15.13:202][ 16]LogAudioMixer: Warning: AudioMixerPlatformInterface Timeout [1824 Seconds] waiting for h/w. InstanceID=1
[2025.06.14-05.15.13:510][ 16]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek High Definition Audio), InstanceID=1
[2025.06.14-05.15.13:510][ 16]LogAudioMixer: Display: Ignoring device swap request, AudioStreamInfo.StreamState: 5
[2025.06.14-05.15.13:510][ 16]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{50d2a92a-72df-437d-bbc5-4077e4cdc144}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.14-05.15.13:536][ 16]LogAudioMixer: Warning: FMixerPlatformXAudio2::OnCriticalError: 0x88880001: UNKNOWN
[2025.06.14-05.15.15:973][ 17]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [[System Default]]
[2025.06.14-05.15.15:973][ 17]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.14-05.15.15:974][ 17]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=AudioMixerPlatformInterface. Timeout waiting for h/w.
[2025.06.14-05.15.16:122][ 19]LogAudioMixer: Error: CreateMasteringVoice failed with result 0x88890004: AUDCLNT_E_DEVICE_INVALIDATED (line: 1277) with Args (NumChannels=2, SampleRate=44100, DeviceID={0.0.0.00000000}.{6ad5ce67-540d-4848-b0f6-966cd50a1c77}, Name=Headphones (Probuds N41 Stereo))
[2025.06.14-05.15.16:365][ 19]LogAudioMixer: Warning: FAudioMixerPlatformSwappable::CheckThreadedDeviceSwap PostDeviceSwap() failed
[2025.06.14-05.15.16:366][ 19]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.14-05.15.16:366][ 19]LogAudioMixer: Display: StartRunningNullDevice() called, InstanceID=1
[2025.06.14-05.15.16:366][ 19]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.14-05.15.16:366][ 19]LogAudioMixer: Display: FMixerNullCallback: Simulating a h/w device callback at [21ms], ThreadID=21536
[2025.06.14-05.15.16:366][ 19]LogAudioMixer: Display: Audio Buffer Underrun (starvation) detected. InstanceID=1
[2025.06.14-05.15.16:409][ 19]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.14-05.15.21:615][ 35]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Headphones (Probuds N41 Stereo), InstanceID=1
[2025.06.14-05.15.21:615][ 35]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{6ad5ce67-540d-4848-b0f6-966cd50a1c77}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.14-05.15.21:714][ 35]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{6ad5ce67-540d-4848-b0f6-966cd50a1c77}]
[2025.06.14-05.15.21:714][ 35]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.14-05.15.21:714][ 35]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.14-05.15.22:379][ 37]LogAudioMixer: Display: StopRunningNullDevice() called, InstanceID=1
[2025.06.14-05.15.22:382][ 37]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=44100, DeviceID={0.0.0.00000000}.{6ad5ce67-540d-4848-b0f6-966cd50a1c77}, Name=Headphones (Probuds N41 Stereo)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=394.86
[2025.06.14-05.15.22:382][ 37]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.14-05.15.22:382][ 37]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.14-05.15.22:418][ 37]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.14-05.16.09:427][238]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4170.166016
[2025.06.14-05.16.10:436][241]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-05.16.10:436][241]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4170.846680, Update Interval: 331.846680
[2025.06.14-05.22.28:884][376]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4549.620117
[2025.06.14-05.22.29:884][379]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-05.22.29:884][379]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4550.286621, Update Interval: 312.035889
[2025.06.14-05.28.21:353][433]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4902.089355
[2025.06.14-05.28.22:367][437]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-05.28.22:367][437]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4902.757324, Update Interval: 309.754333
[2025.06.14-05.29.05:110][844]LogSlate: Took 0.001101 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.14-05.29.05:745][845]LogFactory: FactoryCreateFile: DataTable with ReimportDataTableFactory (0 1 C:/AISquadmate/Content/AI/Configurations/TDM_AI_Config.json)
[2025.06.14-05.38.36:428][845]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.001 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.14-05.44.11:027][845]LogSlate: Window 'DataTable Options' being destroyed
[2025.06.14-05.44.11:318][845]LogUObjectHash: Compacting FUObjectHashTables data took   5.07ms
[2025.06.14-05.44.11:350][845]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.06.14-05.44.14:393][929]AssetReimport: Importing new asset /Game/AI/Configurations/TDM_AI_Config.
[2025.06.14-05.44.14:393][929]AssetReimport: Failed to import file C:/AISquadmate/Content/AI/Configurations/TDM_AI_Config.json.
[2025.06.14-05.44.45:624][249]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5886.356445
[2025.06.14-05.44.46:958][253]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-05.44.46:958][253]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5887.356445, Update Interval: 310.792572
[2025.06.14-05.45.41:878][418]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Headset (Probuds N41 Hands-Free AG Audio), InstanceID=1
[2025.06.14-05.45.41:878][418]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{ac7d2a92-7e49-47be-9ee9-2eae16a81914}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.14-05.45.41:968][418]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{ac7d2a92-7e49-47be-9ee9-2eae16a81914}]
[2025.06.14-05.45.41:968][418]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.14-05.45.41:968][418]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.14-05.45.42:970][421]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=16000, DeviceID={0.0.0.00000000}.{ac7d2a92-7e49-47be-9ee9-2eae16a81914}, Name=Headset (Probuds N41 Hands-Free AG Audio)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=823.60
[2025.06.14-05.45.42:970][421]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.14-05.45.42:970][421]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.14-05.45.42:973][421]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.14-05.46.27:247][554]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek High Definition Audio), InstanceID=1
[2025.06.14-05.46.27:247][554]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{50d2a92a-72df-437d-bbc5-4077e4cdc144}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.14-05.46.27:312][554]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{50d2a92a-72df-437d-bbc5-4077e4cdc144}]
[2025.06.14-05.46.27:313][554]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.14-05.46.27:313][554]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.14-05.46.28:649][558]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=48000, DeviceID={0.0.0.00000000}.{50d2a92a-72df-437d-bbc5-4077e4cdc144}, Name=Speakers (Realtek High Definition Audio)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=1332.79
[2025.06.14-05.46.28:649][558]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.14-05.46.28:649][558]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.14-05.46.28:651][558]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.14-05.50.38:281][374]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6239.014648
[2025.06.14-05.50.39:281][377]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-05.50.39:281][377]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6239.681152, Update Interval: 311.080048
[2025.06.14-05.51.03:331][449]LogFactory: FactoryCreateFile: CurveBase with ReimportCurveFactory (0 1 C:/AISquadmate/Content/Data/DataTables/DT_WeaponStats.csv)
[2025.06.14-06.19.12:287][449]LogSlate: Window 'DataTable Options' being destroyed
[2025.06.14-06.19.12:309][449]LogCSVImportFactory: Imported DataTable 'DT_WeaponStats' - 15 Problems
[2025.06.14-06.19.12:309][449]LogCSVImportFactory: 0:Cannot find Property for column 'WeaponName' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 1:Cannot find Property for column 'WeaponType' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 2:Cannot find Property for column 'Damage' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 3:Cannot find Property for column 'FireRate' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 4:Cannot find Property for column 'Range' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 5:Cannot find Property for column 'Accuracy' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 6:Cannot find Property for column 'Stability' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 7:Cannot find Property for column 'MagazineSize' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 8:Cannot find Property for column 'ReloadTime' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 9:Cannot find Property for column 'RecoilVertical' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 10:Cannot find Property for column 'RecoilHorizontal' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 11:Cannot find Property for column 'IsAutomatic' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 12:Cannot find Property for column 'CanADS' in struct 'GameplayTagTableRow'.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 13:Expected column 'Tag' not found in input.
[2025.06.14-06.19.12:310][449]LogCSVImportFactory: 14:Expected column 'DevComment' not found in input.
