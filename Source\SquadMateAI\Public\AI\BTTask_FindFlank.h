#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "EnvironmentQuery/EnvQueryTypes.h"
#include "Engine/DataTable.h"
#include "TDMAIController.h"
#include "BTTask_FindFlank.generated.h"

// Flanking parameters for different tactical approaches
USTRUCT(BlueprintType)
struct FFlankingParams : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float MinFlankDistance = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float MaxFlankDistance = 1200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float OptimalFlankAngle = 90.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float FlankAngleTolerance = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float CoverRequirement = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float StealthFactor = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    bool bRequireLineOfSight = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    bool bAvoidAllyPositions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float MinAllyDistance = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    float PreferredElevation = 50.0f;

    FFlankingParams()
    {
        MinFlankDistance = 400.0f;
        MaxFlankDistance = 1200.0f;
        OptimalFlankAngle = 90.0f;
        FlankAngleTolerance = 45.0f;
        CoverRequirement = 0.7f;
        StealthFactor = 0.5f;
        bRequireLineOfSight = true;
        bAvoidAllyPositions = true;
        MinAllyDistance = 200.0f;
        PreferredElevation = 50.0f;
    }
};

/**
 * Behavior Tree task that uses EQS to find optimal flanking positions
 * Considers enemy facing direction, cover availability, and tactical advantage
 */
UCLASS(BlueprintType, meta=(DisplayName="Find Flank Position (EQS)"))
class SQUADMATEAI_API UBTTask_FindFlank : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_FindFlank();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // EQS Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* FlankQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* StealthFlankQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* AggressiveFlankQuery;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector FlankLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TacticStateKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector SquadRoleKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector SquadMembersKey;

    // Flanking Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Parameters")
    FFlankingParams DefaultParams;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Parameters")
    FFlankingParams StealthParams;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Parameters")
    FFlankingParams AggressiveParams;

    // Role-Based Adjustments
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Role Adjustments")
    TMap<ETDMSquadRole, FFlankingParams> RoleBasedParams;

    // Query Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryRadius = 1500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryTimeLimit = 2.0f;

    // Movement Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bMoveToFlankPosition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float MovementSpeed = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float AcceptableRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bUseStealthMovement = false;

private:
    // EQS Query handling
    void StartFlankQuery(UBehaviorTreeComponent& OwnerComp);
    void OnEQSQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp);
    
    // Query selection and configuration
    UEnvQuery* SelectOptimalQuery(UBehaviorTreeComponent& OwnerComp);
    void ConfigureQueryParams(FEnvQueryRequest& QueryRequest, UBehaviorTreeComponent& OwnerComp);
    FFlankingParams GetAdjustedParams(UBehaviorTreeComponent& OwnerComp);

    // Utility functions
    AActor* GetCurrentTarget(UBehaviorTreeComponent& OwnerComp);
    ETDMTacticState GetCurrentTactic(UBehaviorTreeComponent& OwnerComp);
    ETDMSquadRole GetSquadRole(UBehaviorTreeComponent& OwnerComp);
    TArray<AActor*> GetSquadMembers(UBehaviorTreeComponent& OwnerComp);
    
    // Flanking evaluation
    bool ValidateFlankPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp);
    float CalculateFlankAngle(const FVector& FlankPos, const FVector& EnemyPos, const FVector& EnemyForward);
    bool IsPositionBehindEnemy(const FVector& FlankPos, const FVector& EnemyPos, const FVector& EnemyForward);
    
    // Movement handling
    void InitiateFlankMovement(const FVector& FlankLocation, UBehaviorTreeComponent& OwnerComp);

    // Cached references
    TWeakObjectPtr<UBehaviorTreeComponent> CachedOwnerComp;
    FEnvQueryRequest ActiveQueryRequest;

public:
    // Static utility functions for flanking calculations
    UFUNCTION(BlueprintCallable, Category = "Flanking", CallInEditor = true)
    static float ScoreFlankPosition(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                  const FVector& AgentPosition, const FFlankingParams& Params);

    UFUNCTION(BlueprintCallable, Category = "Flanking")
    static bool IsValidFlankAngle(const FVector& FlankPos, const FVector& EnemyPos, 
                                const FVector& EnemyForward, float OptimalAngle, float Tolerance);
};
