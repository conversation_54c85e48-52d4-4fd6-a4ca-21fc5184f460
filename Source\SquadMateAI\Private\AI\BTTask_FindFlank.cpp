#include "AI/BTTask_FindFlank.h"
#include "AI/TDMAIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "EnvironmentQuery/EnvQuery.h"
#include "EnvironmentQuery/EnvQueryManager.h"
#include "AIController.h"
#include "NavigationSystem.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Kismet/KismetMathLibrary.h"

UBTTask_FindFlank::UBTTask_FindFlank()
{
    NodeName = "Find Flank Position (EQS)";
    bNotifyTick = false;
    bNotifyTaskFinished = true;

    // Initialize default blackboard keys
    TargetActorKey.SelectedKeyName = "TargetActor";
    FlankLocationKey.SelectedKeyName = "FlankLocation";
    TacticStateKey.SelectedKeyName = "TacticState";
    SquadRoleKey.SelectedKeyName = "SquadRole";
    SquadMembersKey.SelectedKeyName = "SquadMembers";

    // Configure stealth parameters for Scout role
    StealthParams.MinFlankDistance = 600.0f;
    StealthParams.MaxFlankDistance = 1500.0f;
    StealthParams.StealthFactor = 0.9f;
    StealthParams.CoverRequirement = 0.8f;
    StealthParams.bRequireLineOfSight = false;

    // Configure aggressive parameters for Assault role
    AggressiveParams.MinFlankDistance = 300.0f;
    AggressiveParams.MaxFlankDistance = 800.0f;
    AggressiveParams.StealthFactor = 0.2f;
    AggressiveParams.CoverRequirement = 0.4f;
    AggressiveParams.bRequireLineOfSight = true;
}

EBTNodeResult::Type UBTTask_FindFlank::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    APawn* ControlledPawn = AIController ? AIController->GetPawn() : nullptr;

    if (!AIController || !ControlledPawn)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindFlank: Invalid AI Controller or Pawn"));
        return EBTNodeResult::Failed;
    }

    // Check if we have a valid target
    AActor* CurrentTarget = GetCurrentTarget(OwnerComp);
    if (!CurrentTarget)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindFlank: No valid target for flanking"));
        return EBTNodeResult::Failed;
    }

    // Cache the owner component for async callback
    CachedOwnerComp = &OwnerComp;

    // Start the EQS query
    StartFlankQuery(OwnerComp);

    return EBTNodeResult::InProgress;
}

void UBTTask_FindFlank::StartFlankQuery(UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    APawn* ControlledPawn = AIController->GetPawn();

    // Select the appropriate query based on role and tactic
    UEnvQuery* QueryToUse = SelectOptimalQuery(OwnerComp);
    if (!QueryToUse)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindFlank: No valid EQS query found"));
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Create and configure the query request
    FEnvQueryRequest QueryRequest(QueryToUse, ControlledPawn);
    ConfigureQueryParams(QueryRequest, OwnerComp);

    // Execute the query
    ActiveQueryRequest = QueryRequest;
    QueryRequest.Execute(EEnvQueryRunMode::SingleResult, AIController,
        FQueryFinishedSignature::CreateUObject(this, &UBTTask_FindFlank::OnEQSQueryComplete, &OwnerComp));
}

UEnvQuery* UBTTask_FindFlank::SelectOptimalQuery(UBehaviorTreeComponent& OwnerComp)
{
    ETDMSquadRole CurrentRole = GetSquadRole(OwnerComp);
    ETDMTacticState CurrentTactic = GetCurrentTactic(OwnerComp);

    // Select query based on role and tactic
    switch (CurrentRole)
    {
        case ETDMSquadRole::Scout:
            return StealthFlankQuery ? StealthFlankQuery : FlankQuery;
            
        case ETDMSquadRole::Assault:
            return AggressiveFlankQuery ? AggressiveFlankQuery : FlankQuery;
            
        default:
            return FlankQuery;
    }
}

void UBTTask_FindFlank::ConfigureQueryParams(FEnvQueryRequest& QueryRequest, UBehaviorTreeComponent& OwnerComp)
{
    FFlankingParams CurrentParams = GetAdjustedParams(OwnerComp);
    AActor* CurrentTarget = GetCurrentTarget(OwnerComp);

    // Set query parameters
    QueryRequest.SetFloatParam("QueryRadius", QueryRadius);
    QueryRequest.SetFloatParam("MinFlankDistance", CurrentParams.MinFlankDistance);
    QueryRequest.SetFloatParam("MaxFlankDistance", CurrentParams.MaxFlankDistance);
    QueryRequest.SetFloatParam("OptimalFlankAngle", CurrentParams.OptimalFlankAngle);
    QueryRequest.SetFloatParam("FlankAngleTolerance", CurrentParams.FlankAngleTolerance);
    QueryRequest.SetFloatParam("CoverRequirement", CurrentParams.CoverRequirement);
    QueryRequest.SetFloatParam("StealthFactor", CurrentParams.StealthFactor);
    QueryRequest.SetFloatParam("PreferredElevation", CurrentParams.PreferredElevation);

    // Set context actors
    if (CurrentTarget)
    {
        QueryRequest.SetActorParam("EnemyTarget", CurrentTarget);
    }

    TArray<AActor*> SquadMembers = GetSquadMembers(OwnerComp);
    if (SquadMembers.Num() > 0)
    {
        QueryRequest.SetActorsParam("SquadMembers", SquadMembers);
    }
}

FFlankingParams UBTTask_FindFlank::GetAdjustedParams(UBehaviorTreeComponent& OwnerComp)
{
    ETDMSquadRole CurrentRole = GetSquadRole(OwnerComp);
    ETDMTacticState CurrentTactic = GetCurrentTactic(OwnerComp);

    // Check for role-specific parameters
    if (RoleBasedParams.Contains(CurrentRole))
    {
        return RoleBasedParams[CurrentRole];
    }

    // Select based on current tactic and role
    switch (CurrentRole)
    {
        case ETDMSquadRole::Scout:
            return StealthParams;
            
        case ETDMSquadRole::Assault:
            return AggressiveParams;
            
        default:
            return DefaultParams;
    }
}

void UBTTask_FindFlank::OnEQSQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp)
{
    if (!OwnerComp || !Result.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindFlank: Invalid query result or owner component"));
        if (OwnerComp)
        {
            FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
        }
        return;
    }

    if (Result->IsSuccessful() && Result->Items.Num() > 0)
    {
        // Get the best flank location
        FVector FlankLocation = Result->GetItemAsLocation(0);
        
        // Validate the position
        if (ValidateFlankPosition(FlankLocation, *OwnerComp))
        {
            // Store in blackboard
            UBlackboardComponent* BlackboardComp = OwnerComp->GetBlackboardComponent();
            if (BlackboardComp)
            {
                BlackboardComp->SetValueAsVector(FlankLocationKey.SelectedKeyName, FlankLocation);
                
                // Debug visualization
                if (AAIController* AIController = OwnerComp->GetAIOwner())
                {
                    DrawDebugSphere(AIController->GetWorld(), FlankLocation, 120.0f, 12, FColor::Orange, false, 5.0f);
                    
                    // Draw line to target to show flanking angle
                    AActor* Target = GetCurrentTarget(*OwnerComp);
                    if (Target)
                    {
                        DrawDebugLine(AIController->GetWorld(), FlankLocation, Target->GetActorLocation(), 
                                    FColor::Red, false, 5.0f, 0, 3.0f);
                    }
                }
            }

            // Initiate movement if configured
            if (bMoveToFlankPosition)
            {
                InitiateFlankMovement(FlankLocation, *OwnerComp);
            }

            FinishLatentTask(*OwnerComp, EBTNodeResult::Succeeded);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("BTTask_FindFlank: Flank position validation failed"));
            FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FindFlank: EQS query failed to find valid flank position"));
        FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
    }
}

AActor* UBTTask_FindFlank::GetCurrentTarget(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        return Cast<AActor>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
    }
    return nullptr;
}

ETDMTacticState UBTTask_FindFlank::GetCurrentTactic(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        return static_cast<ETDMTacticState>(BlackboardComp->GetValueAsEnum(TacticStateKey.SelectedKeyName));
    }
    return ETDMTacticState::Patrol;
}

ETDMSquadRole UBTTask_FindFlank::GetSquadRole(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        return static_cast<ETDMSquadRole>(BlackboardComp->GetValueAsEnum(SquadRoleKey.SelectedKeyName));
    }
    return ETDMSquadRole::Assault;
}

TArray<AActor*> UBTTask_FindFlank::GetSquadMembers(UBehaviorTreeComponent& OwnerComp)
{
    TArray<AActor*> SquadMembers;
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        // Implementation depends on how squad members are stored in blackboard
        // This is a placeholder - adjust based on your squad system
    }
    return SquadMembers;
}

bool UBTTask_FindFlank::ValidateFlankPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    AActor* Target = GetCurrentTarget(OwnerComp);
    
    if (!AIController || !AIController->GetPawn() || !Target)
    {
        return false;
    }

    // Check if position is reachable via navigation
    UNavigationSystemV1* NavSys = UNavigationSystemV1::GetCurrent(AIController->GetWorld());
    if (NavSys)
    {
        FNavLocation NavLocation;
        if (!NavSys->ProjectPointToNavigation(Position, NavLocation, FVector(100.0f, 100.0f, 100.0f)))
        {
            return false;
        }
    }

    // Validate flanking angle
    FFlankingParams CurrentParams = GetAdjustedParams(OwnerComp);
    FVector EnemyForward = Target->GetActorForwardVector();
    
    return IsValidFlankAngle(Position, Target->GetActorLocation(), EnemyForward, 
                           CurrentParams.OptimalFlankAngle, CurrentParams.FlankAngleTolerance);
}

void UBTTask_FindFlank::InitiateFlankMovement(const FVector& FlankLocation, UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (AIController)
    {
        // Use stealth movement if configured
        if (bUseStealthMovement)
        {
            // Implement stealth movement logic here
            AIController->MoveToLocation(FlankLocation, AcceptableRadius, false);
        }
        else
        {
            AIController->MoveToLocation(FlankLocation, AcceptableRadius);
        }
    }
}

float UBTTask_FindFlank::ScoreFlankPosition(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                          const FVector& AgentPosition, const FFlankingParams& Params)
{
    float Score = 0.0f;
    
    // Distance scoring
    float Distance = FVector::Dist(FlankPosition, EnemyPosition);
    if (Distance >= Params.MinFlankDistance && Distance <= Params.MaxFlankDistance)
    {
        Score += 0.3f;
    }
    
    // Angle scoring (simplified)
    FVector ToFlank = (FlankPosition - EnemyPosition).GetSafeNormal();
    FVector EnemyForward = FVector::ForwardVector; // Simplified - should use actual enemy forward
    float Angle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(ToFlank, EnemyForward)));
    
    if (FMath::Abs(Angle - Params.OptimalFlankAngle) <= Params.FlankAngleTolerance)
    {
        Score += 0.4f;
    }
    
    return FMath::Clamp(Score, 0.0f, 1.0f);
}

bool UBTTask_FindFlank::IsValidFlankAngle(const FVector& FlankPos, const FVector& EnemyPos, 
                                        const FVector& EnemyForward, float OptimalAngle, float Tolerance)
{
    FVector ToFlank = (FlankPos - EnemyPos).GetSafeNormal();
    float Angle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(ToFlank, EnemyForward)));
    
    return FMath::Abs(Angle - OptimalAngle) <= Tolerance;
}

void UBTTask_FindFlank::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
    // Clean up any active queries
    ActiveQueryRequest = FEnvQueryRequest();
    CachedOwnerComp.Reset();
    
    Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_FindFlank::GetStaticDescription() const
{
    return FString::Printf(TEXT("Find Flank Position using EQS\nDefault Query: %s\nStealth Query: %s\nAggressive Query: %s"), 
        FlankQuery ? *FlankQuery->GetName() : TEXT("None"),
        StealthFlankQuery ? *StealthFlankQuery->GetName() : TEXT("None"),
        AggressiveFlankQuery ? *AggressiveFlankQuery->GetName() : TEXT("None"));
}
