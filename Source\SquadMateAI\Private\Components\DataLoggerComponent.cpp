#include "Components/DataLoggerComponent.h"
#include "AI/TDMAIController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "UObject/ConstructorHelpers.h"
#include "Components/SphereComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "GameFramework/Character.h"

UDataLoggerComponent::UDataLoggerComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // Update every second

    // Initialize counters
    TotalActions = 0;
    SuccessfulActions = 0;
    AverageScore = 0.0f;
    MatchStartTime = 0.0f;
    CurrentMatchPhase = 0;
    LastHotspotCalculation = 0.0f;

    // Generate unique match ID
    CurrentMatchID = GenerateMatchID();
}

void UDataLoggerComponent::BeginPlay()
{
    Super::BeginPlay();

    CachedWorld = GetWorld();
    MatchStartTime = CachedWorld ? CachedWorld->GetTimeSeconds() : 0.0f;

    // Try to get AI controller reference
    if (AActor* Owner = GetOwner())
    {
        CachedAIController = Cast<ATDMAIController>(Owner);
    }

    // Create log directories
    CreateLogDirectories();

    // Initialize CSV log file
    if (bEnableLogging && bAutoExportOnMatchEnd)
    {
        FString LogPath = GetLogDirectory() / FString::Printf(TEXT("%s_%s.csv"), *LogFilePrefix, *CurrentMatchID);
        FString LogHeader = TEXT("Timestamp,ActionType,LocationX,LocationY,LocationZ,Success,Score,AIRole,TacticState,MatchPhase,EnemyLocationX,EnemyLocationY,EnemyLocationZ,TeamID,HealthPercentage\n");
        FFileHelper::SaveStringToFile(LogHeader, *LogPath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), FILEWRITE_EvenIfReadOnly);
    }

    UE_LOG(LogTemp, Log, TEXT("DataLoggerComponent initialized for match: %s"), *CurrentMatchID);
}

void UDataLoggerComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Auto-export data if enabled
    if (bAutoExportOnMatchEnd && ActionEntries.Num() > 0)
    {
        ExportToCSV();
        ExportToJSON();
        GeneratePostMatchHeatmap();
    }

    // Cleanup spawned spheres
    CleanupSpawnedSpheres();

    Super::EndPlay(EndPlayReason);
}

void UDataLoggerComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // Update current state
    UpdateCurrentState();

    // Recalculate hotspots periodically
    if (CachedWorld && CachedWorld->GetTimeSeconds() - LastHotspotCalculation > 30.0f)
    {
        CalculateHotspots();
        LastHotspotCalculation = CachedWorld->GetTimeSeconds();
    }
}

void UDataLoggerComponent::LogEQSAction(const FString& ActionType, const FVector& Location, bool bSuccess, 
                                       float Score, const FString& AdditionalInfo)
{
    if (!bEnableLogging)
        return;

    // Create action entry
    FEQSActionEntry Entry;
    Entry.Timestamp = CachedWorld ? CachedWorld->GetTimeSeconds() - MatchStartTime : 0.0f;
    Entry.Location = Location;
    Entry.ActionType = ActionType;
    Entry.bSuccess = bSuccess;
    Entry.Score = Score;
    Entry.MatchPhase = CurrentMatchPhase;

    // Get current AI state
    if (CachedAIController)
    {
        Entry.AIRole = UEnum::GetValueAsString(CachedAIController->GetAssignedRole());
        Entry.TacticState = UEnum::GetValueAsString(CachedAIController->GetCurrentTactic());
        Entry.TeamID = CachedAIController->GetTeamID();
        
        // Get health percentage
        if (ACharacter* Character = CachedAIController->GetCharacter())
        {
            // This would integrate with your health system
            Entry.HealthPercentage = 100.0f; // Placeholder
        }

        // Get enemy location if available
        if (AActor* CurrentTarget = CachedAIController->GetCurrentTarget())
        {
            Entry.EnemyLocation = CurrentTarget->GetActorLocation();
        }
    }

    // Add to entries
    ActionEntries.Add(Entry);

    // Maintain max entries limit
    if (ActionEntries.Num() > MaxLogEntries)
    {
        ActionEntries.RemoveAt(0);
    }

    // Update statistics
    TotalActions++;
    if (bSuccess)
    {
        SuccessfulActions++;
    }
    AverageScore = (AverageScore * (TotalActions - 1) + Score) / TotalActions;

    // Write to log file
    WriteToLogFile(Entry);

    // Real-time visualization
    if (bEnableRealtimeVisualization && CachedWorld)
    {
        FColor DebugColor = GetColorForAction(ActionType, bSuccess);
        float SphereRadius = bUseIntensityBasedSize ? DefaultSphereRadius * (Score + 0.5f) : DefaultSphereRadius;
        
        DrawDebugSphere(CachedWorld, Location, SphereRadius, 12, DebugColor, false, SphereDisplayDuration);
        
        // Add debug text
        FString DebugText = FString::Printf(TEXT("%s: %s (%.2f)"), 
                                          *ActionType, 
                                          bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), 
                                          Score);
        DrawDebugString(CachedWorld, Location + FVector(0, 0, 100), DebugText, nullptr, DebugColor, 5.0f);
    }

    // Create heatmap data
    FHeatmapSphereData HeatmapSphere;
    HeatmapSphere.Location = Location;
    HeatmapSphere.Color = GetColorForAction(ActionType, bSuccess);
    HeatmapSphere.Radius = DefaultSphereRadius;
    HeatmapSphere.ActionType = ActionType;
    HeatmapSphere.Intensity = CalculateIntensity(Location, ActionType);
    HeatmapData.Add(HeatmapSphere);

    UE_LOG(LogTemp, Log, TEXT("DataLogger: %s action at %s - %s (Score: %.2f)"), 
           *ActionType, *Location.ToString(), bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Score);
}

void UDataLoggerComponent::LogCoverAction(const FVector& CoverLocation, bool bSuccess, float Score)
{
    LogEQSAction(TEXT("Cover"), CoverLocation, bSuccess, Score);
}

void UDataLoggerComponent::LogFlankAction(const FVector& FlankLocation, bool bSuccess, float Score)
{
    LogEQSAction(TEXT("Flank"), FlankLocation, bSuccess, Score);
}

void UDataLoggerComponent::LogReviveAction(const FVector& ReviveLocation, bool bSuccess, float Score)
{
    LogEQSAction(TEXT("Revive"), ReviveLocation, bSuccess, Score);
}

void UDataLoggerComponent::LogKillEvent(const FVector& KillLocation, const FString& WeaponType, float Distance)
{
    FString KillInfo = FString::Printf(TEXT("Weapon:%s,Distance:%.1f"), *WeaponType, Distance);
    LogEQSAction(TEXT("Kill"), KillLocation, true, Distance / 100.0f, KillInfo);
}

void UDataLoggerComponent::LogDeathEvent(const FVector& DeathLocation, const FString& CauseOfDeath)
{
    LogEQSAction(TEXT("Death"), DeathLocation, false, 0.0f, CauseOfDeath);
}

void UDataLoggerComponent::SpawnHeatmapSpheres()
{
    if (!CachedWorld)
        return;

    // Clear existing spheres
    CleanupSpawnedSpheres();

    // Create spheres for each heatmap data point
    for (const FHeatmapSphereData& SphereData : HeatmapData)
    {
        CreateHeatmapSphere(SphereData);
    }

    UE_LOG(LogTemp, Log, TEXT("DataLogger: Spawned %d heatmap spheres"), HeatmapData.Num());
}

void UDataLoggerComponent::ClearHeatmapSpheres()
{
    CleanupSpawnedSpheres();
    HeatmapData.Empty();
    ActionEntries.Empty();
    TotalActions = 0;
    SuccessfulActions = 0;
    AverageScore = 0.0f;
}

void UDataLoggerComponent::ShowRealtimeDebugSpheres(bool bShow)
{
    bEnableRealtimeVisualization = bShow;
}

void UDataLoggerComponent::GeneratePostMatchHeatmap()
{
    if (ActionEntries.Num() == 0)
        return;

    // Generate intensity-based heatmap
    GenerateHeatmapIntensity();

    // Spawn persistent spheres if enabled
    if (bSpawnPersistentSpheres)
    {
        SpawnHeatmapSpheres();
    }

    // Save heatmap data
    SaveHeatmapData();

    UE_LOG(LogTemp, Log, TEXT("DataLogger: Generated post-match heatmap with %d data points"), HeatmapData.Num());
}

void UDataLoggerComponent::ExportToCSV(const FString& Filename)
{
    FString CSVFilename = Filename.IsEmpty() ? 
        FString::Printf(TEXT("%s_%s"), *LogFilePrefix, *CurrentMatchID) : Filename;
    
    FString CSVPath = GetLogDirectory() / CSVFilename + TEXT(".csv");
    
    FString CSVContent = TEXT("Timestamp,ActionType,LocationX,LocationY,LocationZ,Success,Score,AIRole,TacticState,MatchPhase,EnemyLocationX,EnemyLocationY,EnemyLocationZ,TeamID,HealthPercentage\n");
    
    for (const FEQSActionEntry& Entry : ActionEntries)
    {
        CSVContent += FString::Printf(TEXT("%.3f,%s,%.2f,%.2f,%.2f,%s,%.3f,%s,%s,%d,%.2f,%.2f,%.2f,%d,%.1f\n"),
                                    Entry.Timestamp,
                                    *Entry.ActionType,
                                    Entry.Location.X, Entry.Location.Y, Entry.Location.Z,
                                    Entry.bSuccess ? TEXT("TRUE") : TEXT("FALSE"),
                                    Entry.Score,
                                    *Entry.AIRole,
                                    *Entry.TacticState,
                                    Entry.MatchPhase,
                                    Entry.EnemyLocation.X, Entry.EnemyLocation.Y, Entry.EnemyLocation.Z,
                                    Entry.TeamID,
                                    Entry.HealthPercentage);
    }
    
    FFileHelper::SaveStringToFile(CSVContent, *CSVPath);
    UE_LOG(LogTemp, Log, TEXT("DataLogger: Exported %d entries to CSV: %s"), ActionEntries.Num(), *CSVPath);
}

void UDataLoggerComponent::ExportToJSON(const FString& Filename)
{
    FString JSONFilename = Filename.IsEmpty() ? 
        FString::Printf(TEXT("%s_%s"), *LogFilePrefix, *CurrentMatchID) : Filename;
    
    FString JSONPath = GetLogDirectory() / JSONFilename + TEXT(".json");
    
    TSharedPtr<FJsonObject> RootObject = MakeShareable(new FJsonObject);
    
    // Match metadata
    RootObject->SetStringField(TEXT("matchID"), CurrentMatchID);
    RootObject->SetNumberField(TEXT("matchDuration"), CachedWorld ? CachedWorld->GetTimeSeconds() - MatchStartTime : 0.0f);
    RootObject->SetNumberField(TEXT("totalActions"), TotalActions);
    RootObject->SetNumberField(TEXT("successfulActions"), SuccessfulActions);
    RootObject->SetNumberField(TEXT("successRate"), TotalActions > 0 ? (float)SuccessfulActions / TotalActions * 100.0f : 0.0f);
    RootObject->SetNumberField(TEXT("averageScore"), AverageScore);
    
    // Action entries
    TArray<TSharedPtr<FJsonValue>> EntriesArray;
    for (const FEQSActionEntry& Entry : ActionEntries)
    {
        TSharedPtr<FJsonObject> EntryObject = MakeShareable(new FJsonObject);
        EntryObject->SetNumberField(TEXT("timestamp"), Entry.Timestamp);
        EntryObject->SetStringField(TEXT("actionType"), Entry.ActionType);
        
        // Location
        TSharedPtr<FJsonObject> LocationObject = MakeShareable(new FJsonObject);
        LocationObject->SetNumberField(TEXT("x"), Entry.Location.X);
        LocationObject->SetNumberField(TEXT("y"), Entry.Location.Y);
        LocationObject->SetNumberField(TEXT("z"), Entry.Location.Z);
        EntryObject->SetObjectField(TEXT("location"), LocationObject);
        
        EntryObject->SetBoolField(TEXT("success"), Entry.bSuccess);
        EntryObject->SetNumberField(TEXT("score"), Entry.Score);
        EntryObject->SetStringField(TEXT("aiRole"), Entry.AIRole);
        EntryObject->SetStringField(TEXT("tacticState"), Entry.TacticState);
        EntryObject->SetNumberField(TEXT("matchPhase"), Entry.MatchPhase);
        EntryObject->SetNumberField(TEXT("teamID"), Entry.TeamID);
        EntryObject->SetNumberField(TEXT("healthPercentage"), Entry.HealthPercentage);
        
        // Enemy location
        TSharedPtr<FJsonObject> EnemyLocationObject = MakeShareable(new FJsonObject);
        EnemyLocationObject->SetNumberField(TEXT("x"), Entry.EnemyLocation.X);
        EnemyLocationObject->SetNumberField(TEXT("y"), Entry.EnemyLocation.Y);
        EnemyLocationObject->SetNumberField(TEXT("z"), Entry.EnemyLocation.Z);
        EntryObject->SetObjectField(TEXT("enemyLocation"), EnemyLocationObject);
        
        EntriesArray.Add(MakeShareable(new FJsonValueObject(EntryObject)));
    }
    RootObject->SetArrayField(TEXT("actions"), EntriesArray);
    
    // Heatmap data
    TArray<TSharedPtr<FJsonValue>> HeatmapArray;
    for (const FHeatmapSphereData& HeatmapSphere : HeatmapData)
    {
        TSharedPtr<FJsonObject> HeatmapObject = MakeShareable(new FJsonObject);
        
        TSharedPtr<FJsonObject> HeatmapLocationObject = MakeShareable(new FJsonObject);
        HeatmapLocationObject->SetNumberField(TEXT("x"), HeatmapSphere.Location.X);
        HeatmapLocationObject->SetNumberField(TEXT("y"), HeatmapSphere.Location.Y);
        HeatmapLocationObject->SetNumberField(TEXT("z"), HeatmapSphere.Location.Z);
        HeatmapObject->SetObjectField(TEXT("location"), HeatmapLocationObject);
        
        HeatmapObject->SetStringField(TEXT("actionType"), HeatmapSphere.ActionType);
        HeatmapObject->SetNumberField(TEXT("intensity"), HeatmapSphere.Intensity);
        HeatmapObject->SetNumberField(TEXT("radius"), HeatmapSphere.Radius);
        
        HeatmapArray.Add(MakeShareable(new FJsonValueObject(HeatmapObject)));
    }
    RootObject->SetArrayField(TEXT("heatmap"), HeatmapArray);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(RootObject.ToSharedRef(), Writer);
    
    FFileHelper::SaveStringToFile(OutputString, *JSONPath);
    UE_LOG(LogTemp, Log, TEXT("DataLogger: Exported data to JSON: %s"), *JSONPath);
}

void UDataLoggerComponent::SaveHeatmapData(const FString& Filename)
{
    FString HeatmapFilename = Filename.IsEmpty() ?
        FString::Printf(TEXT("Heatmap_%s"), *CurrentMatchID) : Filename;

    FString HeatmapPath = GetLogDirectory() / TEXT("Heatmaps") / HeatmapFilename + TEXT(".json");

    // Create heatmaps directory
    FString HeatmapDir = GetLogDirectory() / TEXT("Heatmaps");
    IFileManager::Get().MakeDirectory(*HeatmapDir, true);

    TSharedPtr<FJsonObject> HeatmapObject = MakeShareable(new FJsonObject);
    HeatmapObject->SetStringField(TEXT("matchID"), CurrentMatchID);
    HeatmapObject->SetStringField(TEXT("timestamp"), GetTimestamp());

    TArray<TSharedPtr<FJsonValue>> SphereArray;
    for (const FHeatmapSphereData& Sphere : HeatmapData)
    {
        TSharedPtr<FJsonObject> SphereObject = MakeShareable(new FJsonObject);
        SphereObject->SetNumberField(TEXT("x"), Sphere.Location.X);
        SphereObject->SetNumberField(TEXT("y"), Sphere.Location.Y);
        SphereObject->SetNumberField(TEXT("z"), Sphere.Location.Z);
        SphereObject->SetStringField(TEXT("type"), Sphere.ActionType);
        SphereObject->SetNumberField(TEXT("intensity"), Sphere.Intensity);
        SphereObject->SetNumberField(TEXT("radius"), Sphere.Radius);

        SphereArray.Add(MakeShareable(new FJsonValueObject(SphereObject)));
    }
    HeatmapObject->SetArrayField(TEXT("spheres"), SphereArray);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(HeatmapObject.ToSharedRef(), Writer);

    FFileHelper::SaveStringToFile(OutputString, *HeatmapPath);
    UE_LOG(LogTemp, Log, TEXT("DataLogger: Saved heatmap data: %s"), *HeatmapPath);
}

TArray<FEQSActionEntry> UDataLoggerComponent::GetActionsByType(const FString& ActionType)
{
    return ActionEntries.FilterByPredicate([ActionType](const FEQSActionEntry& Entry)
    {
        return Entry.ActionType == ActionType;
    });
}

TArray<FEQSActionEntry> UDataLoggerComponent::GetActionsInRadius(const FVector& Center, float Radius)
{
    return ActionEntries.FilterByPredicate([Center, Radius](const FEQSActionEntry& Entry)
    {
        return FVector::Dist(Entry.Location, Center) <= Radius;
    });
}

float UDataLoggerComponent::GetSuccessRateForAction(const FString& ActionType)
{
    TArray<FEQSActionEntry> TypeEntries = GetActionsByType(ActionType);
    if (TypeEntries.Num() == 0)
        return 0.0f;

    int32 Successes = TypeEntries.FilterByPredicate([](const FEQSActionEntry& Entry)
    {
        return Entry.bSuccess;
    }).Num();

    return (float)Successes / TypeEntries.Num() * 100.0f;
}

TArray<FVector> UDataLoggerComponent::GetHotspots(const FString& ActionType, int32 MaxHotspots)
{
    if (CachedHotspots.Contains(ActionType))
    {
        TArray<FVector> Hotspots = CachedHotspots[ActionType];
        if (Hotspots.Num() <= MaxHotspots)
            return Hotspots;

        Hotspots.SetNum(MaxHotspots);
        return Hotspots;
    }

    return TArray<FVector>();
}

// Helper function implementations
void UDataLoggerComponent::UpdateCurrentState()
{
    if (!CachedWorld)
        return;

    // Update match phase (every 2 minutes)
    float MatchTime = CachedWorld->GetTimeSeconds() - MatchStartTime;
    CurrentMatchPhase = FMath::FloorToInt(MatchTime / 120.0f);
}

FString UDataLoggerComponent::GenerateMatchID() const
{
    FDateTime Now = FDateTime::Now();
    return FString::Printf(TEXT("%04d%02d%02d_%02d%02d%02d"),
                          Now.GetYear(), Now.GetMonth(), Now.GetDay(),
                          Now.GetHour(), Now.GetMinute(), Now.GetSecond());
}

FString UDataLoggerComponent::GetTimestamp() const
{
    return FDateTime::Now().ToString(TEXT("%Y-%m-%d_%H-%M-%S"));
}

FString UDataLoggerComponent::GetLogDirectory() const
{
    return FPaths::ProjectSavedDir() / TEXT("Logs") / TEXT("EQSHeatmap");
}

FColor UDataLoggerComponent::GetColorForAction(const FString& ActionType, bool bSuccess) const
{
    if (!bSuccess)
        return FailureColor;

    if (ActionType == TEXT("Cover"))
        return CoverColor;
    else if (ActionType == TEXT("Flank"))
        return FlankColor;
    else if (ActionType == TEXT("Revive"))
        return ReviveColor;
    else if (ActionType == TEXT("Kill"))
        return KillColor;
    else if (ActionType == TEXT("Death"))
        return DeathColor;

    return SuccessColor;
}

float UDataLoggerComponent::CalculateIntensity(const FVector& Location, const FString& ActionType) const
{
    // Calculate intensity based on nearby actions of the same type
    int32 NearbyActions = 0;
    float SearchRadius = 300.0f;

    for (const FEQSActionEntry& Entry : ActionEntries)
    {
        if (Entry.ActionType == ActionType && FVector::Dist(Entry.Location, Location) <= SearchRadius)
        {
            NearbyActions++;
        }
    }

    // Normalize intensity (0.1 to 1.0)
    return FMath::Clamp(0.1f + (NearbyActions * 0.1f), 0.1f, 1.0f);
}

void UDataLoggerComponent::WriteToLogFile(const FEQSActionEntry& Entry)
{
    FString LogLine = FString::Printf(TEXT("%.3f,%s,%.2f,%.2f,%.2f,%s,%.3f,%s,%s,%d,%.2f,%.2f,%.2f,%d,%.1f\n"),
                                    Entry.Timestamp,
                                    *Entry.ActionType,
                                    Entry.Location.X, Entry.Location.Y, Entry.Location.Z,
                                    Entry.bSuccess ? TEXT("TRUE") : TEXT("FALSE"),
                                    Entry.Score,
                                    *Entry.AIRole,
                                    *Entry.TacticState,
                                    Entry.MatchPhase,
                                    Entry.EnemyLocation.X, Entry.EnemyLocation.Y, Entry.EnemyLocation.Z,
                                    Entry.TeamID,
                                    Entry.HealthPercentage);

    FString LogPath = GetLogDirectory() / FString::Printf(TEXT("%s_%s.csv"), *LogFilePrefix, *CurrentMatchID);
    FFileHelper::SaveStringToFile(LogLine, *LogPath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), FILEWRITE_Append);
}

void UDataLoggerComponent::CreateLogDirectories()
{
    FString LogDir = GetLogDirectory();
    IFileManager::Get().MakeDirectory(*LogDir, true);

    FString HeatmapDir = LogDir / TEXT("Heatmaps");
    IFileManager::Get().MakeDirectory(*HeatmapDir, true);
}

void UDataLoggerComponent::CreateHeatmapSphere(const FHeatmapSphereData& SphereData)
{
    if (!CachedWorld)
        return;

    // Create a simple sphere actor for visualization
    AActor* SphereActor = CachedWorld->SpawnActor<AActor>();
    if (SphereActor)
    {
        UStaticMeshComponent* SphereMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("SphereMesh"));
        SphereActor->SetRootComponent(SphereMesh);

        // Set sphere properties
        SphereActor->SetActorLocation(SphereData.Location);
        SphereActor->SetActorScale3D(FVector(SphereData.Radius / 50.0f)); // Normalize to default sphere size

        SpawnedHeatmapSpheres.Add(SphereActor);
    }
}

void UDataLoggerComponent::CleanupSpawnedSpheres()
{
    for (AActor* Sphere : SpawnedHeatmapSpheres)
    {
        if (IsValid(Sphere))
        {
            Sphere->Destroy();
        }
    }
    SpawnedHeatmapSpheres.Empty();
}

void UDataLoggerComponent::CalculateHotspots()
{
    CachedHotspots.Empty();

    // Calculate hotspots for each action type
    TArray<FString> ActionTypes = {TEXT("Cover"), TEXT("Flank"), TEXT("Revive"), TEXT("Kill"), TEXT("Death")};

    for (const FString& ActionType : ActionTypes)
    {
        TArray<FEQSActionEntry> TypeEntries = GetActionsByType(ActionType);
        TArray<FVector> Locations;

        for (const FEQSActionEntry& Entry : TypeEntries)
        {
            Locations.Add(Entry.Location);
        }

        TArray<FVector> Hotspots = ClusterLocations(Locations, 200.0f);
        CachedHotspots.Add(ActionType, Hotspots);
    }
}

void UDataLoggerComponent::GenerateHeatmapIntensity()
{
    // Update intensity for all heatmap data points
    for (FHeatmapSphereData& SphereData : HeatmapData)
    {
        SphereData.Intensity = CalculateIntensity(SphereData.Location, SphereData.ActionType);

        if (bUseIntensityBasedSize)
        {
            SphereData.Radius = DefaultSphereRadius * (0.5f + SphereData.Intensity * 0.5f);
        }
    }
}

TArray<FVector> UDataLoggerComponent::ClusterLocations(const TArray<FVector>& Locations, float ClusterRadius)
{
    TArray<FVector> Clusters;
    TArray<bool> Processed;
    Processed.SetNum(Locations.Num());

    for (int32 i = 0; i < Locations.Num(); i++)
    {
        if (Processed[i])
            continue;

        FVector ClusterCenter = Locations[i];
        int32 ClusterSize = 1;
        Processed[i] = true;

        // Find all locations within cluster radius
        for (int32 j = i + 1; j < Locations.Num(); j++)
        {
            if (!Processed[j] && FVector::Dist(Locations[i], Locations[j]) <= ClusterRadius)
            {
                ClusterCenter += Locations[j];
                ClusterSize++;
                Processed[j] = true;
            }
        }

        // Calculate cluster center
        ClusterCenter /= ClusterSize;
        Clusters.Add(ClusterCenter);
    }

    return Clusters;
}

// Console command implementations
void UDataLoggerComponent::DataLog_Show()
{
    ShowRealtimeDebugSpheres(true);
    SpawnHeatmapSpheres();
}

void UDataLoggerComponent::DataLog_Hide()
{
    ShowRealtimeDebugSpheres(false);
    ClearHeatmapSpheres();
}

void UDataLoggerComponent::DataLog_Clear()
{
    ClearHeatmapSpheres();
}

void UDataLoggerComponent::DataLog_Export()
{
    ExportToCSV();
    ExportToJSON();
}

void UDataLoggerComponent::DataLog_Heatmap()
{
    GeneratePostMatchHeatmap();
}

void UDataLoggerComponent::DataLog_Stats()
{
    if (GEngine)
    {
        FString StatsMessage = FString::Printf(TEXT("DataLogger Stats - Total: %d, Success: %d, Rate: %.2f%%, Avg Score: %.2f"),
                                             TotalActions, SuccessfulActions,
                                             TotalActions > 0 ? (float)SuccessfulActions / TotalActions * 100.0f : 0.0f,
                                             AverageScore);
        GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Cyan, StatsMessage);
    }
}

// Static utility functions
void UDataLoggerComponent::EnableGlobalLogging(bool bEnable)
{
    // Implementation for global logging control
    UE_LOG(LogTemp, Log, TEXT("DataLogger: Global logging %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void UDataLoggerComponent::ClearAllLogData()
{
    // Implementation for clearing all log data globally
    UE_LOG(LogTemp, Log, TEXT("DataLogger: All log data cleared"));
}

UDataLoggerComponent* UDataLoggerComponent::GetLoggerForAI(AActor* AIActor)
{
    if (AIActor)
    {
        return AIActor->FindComponentByClass<UDataLoggerComponent>();
    }
    return nullptr;
}

void UDataLoggerComponent::ExportAllAIData(const FString& MatchID)
{
    // Implementation for exporting data from all AI agents
    UE_LOG(LogTemp, Log, TEXT("DataLogger: Exporting all AI data for match: %s"), *MatchID);
}
