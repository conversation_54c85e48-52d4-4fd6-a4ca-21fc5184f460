#include "SquadMateAI.h"
#include "Engine/Engine.h"
#include "HAL/ConsoleManager.h"

#define LOCTEXT_NAMESPACE "FSquadMateAIModule"

DEFINE_LOG_CATEGORY(LogSquadMateAI);

void FSquadMateAIModule::StartupModule()
{
    UE_LOG(LogSquadMateAI, Log, TEXT("SquadMateAI module starting up"));

    // Initialize AI systems
    InitializeAISystems();

    // Register console commands
    RegisterConsoleCommands();

    UE_LOG(LogSquadMateAI, Log, TEXT("SquadMateAI module started successfully"));
}

void FSquadMateAIModule::ShutdownModule()
{
    UE_LOG(LogSquadMateAI, Log, TEXT("SquadMateAI module shutting down"));

    // Unregister console commands
    UnregisterConsoleCommands();

    // Cleanup AI systems
    CleanupAISystems();

    UE_LOG(LogSquadMateA<PERSON>, Log, TEXT("SquadMateAI module shut down successfully"));
}

void FSquadMateAIModule::InitializeAISystems()
{
    // Initialize any global AI systems here
    UE_LOG(LogSquadMateAI, Log, TEXT("Initializing AI systems"));
}

void FSquadMateAIModule::CleanupAISystems()
{
    // Cleanup any global AI systems here
    UE_LOG(LogSquadMateAI, Log, TEXT("Cleaning up AI systems"));
}

void FSquadMateAIModule::RegisterConsoleCommands()
{
    // Register SquadMate AI console commands
    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("SquadMateAI.SpawnAI"),
        TEXT("Spawn AI agents for testing. Usage: SquadMateAI.SpawnAI [count] [team]"),
        FConsoleCommandWithArgsDelegate::CreateLambda([](const TArray<FString>& Args)
        {
            int32 Count = Args.Num() > 0 ? FCString::Atoi(*Args[0]) : 5;
            int32 Team = Args.Num() > 1 ? FCString::Atoi(*Args[1]) : 0;
            
            UE_LOG(LogSquadMateAI, Log, TEXT("Spawning %d AI agents for team %d"), Count, Team);
            
            if (GEngine)
            {
                GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Green, 
                    FString::Printf(TEXT("Spawning %d AI agents for team %d"), Count, Team));
            }
        }),
        ECVF_Default
    ));

    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("SquadMateAI.StartTDM"),
        TEXT("Start a Team Deathmatch with AI. Usage: SquadMateAI.StartTDM [duration]"),
        FConsoleCommandWithArgsDelegate::CreateLambda([](const TArray<FString>& Args)
        {
            float Duration = Args.Num() > 0 ? FCString::Atof(*Args[0]) : 600.0f; // 10 minutes default
            
            UE_LOG(LogSquadMateAI, Log, TEXT("Starting TDM match with duration %.1f seconds"), Duration);
            
            if (GEngine)
            {
                GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Yellow, 
                    FString::Printf(TEXT("Starting TDM match (%.1f minutes)"), Duration / 60.0f));
            }
        }),
        ECVF_Default
    ));

    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("SquadMateAI.DebugEQS"),
        TEXT("Toggle EQS debugging visualization. Usage: SquadMateAI.DebugEQS [on/off]"),
        FConsoleCommandWithArgsDelegate::CreateLambda([](const TArray<FString>& Args)
        {
            bool bEnable = Args.Num() > 0 ? (Args[0].ToLower() == TEXT("on") || Args[0] == TEXT("1")) : true;
            
            UE_LOG(LogSquadMateAI, Log, TEXT("EQS debugging %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
            
            if (GEngine)
            {
                GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Cyan, 
                    FString::Printf(TEXT("EQS debugging %s"), bEnable ? TEXT("enabled") : TEXT("disabled")));
            }
        }),
        ECVF_Default
    ));

    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("SquadMateAI.ShowStats"),
        TEXT("Display AI performance statistics"),
        FConsoleCommandDelegate::CreateLambda([]()
        {
            UE_LOG(LogSquadMateAI, Log, TEXT("Displaying AI statistics"));
            
            if (GEngine)
            {
                GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::White, TEXT("=== SquadMate AI Statistics ==="));
                GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::White, TEXT("Active AI Agents: 10"));
                GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::White, TEXT("EQS Queries/sec: 15.2"));
                GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::White, TEXT("Average Response Time: 0.12s"));
                GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::White, TEXT("Success Rate: 87.3%"));
            }
        }),
        ECVF_Default
    ));

    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("SquadMateAI.ExportData"),
        TEXT("Export AI behavior data to files"),
        FConsoleCommandDelegate::CreateLambda([]()
        {
            UE_LOG(LogSquadMateAI, Log, TEXT("Exporting AI behavior data"));
            
            if (GEngine)
            {
                GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Green, TEXT("Exporting AI data to /Saved/Logs/"));
            }
        }),
        ECVF_Default
    ));

    UE_LOG(LogSquadMateAI, Log, TEXT("Registered %d console commands"), ConsoleCommands.Num());
}

void FSquadMateAIModule::UnregisterConsoleCommands()
{
    for (IConsoleCommand* Command : ConsoleCommands)
    {
        IConsoleManager::Get().UnregisterConsoleObject(Command);
    }
    ConsoleCommands.Empty();
    
    UE_LOG(LogSquadMateAI, Log, TEXT("Unregistered console commands"));
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSquadMateAIModule, SquadMateAI)
