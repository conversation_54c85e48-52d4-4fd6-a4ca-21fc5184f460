#include "Components/EQSDebuggerComponent.h"
#include "AI/TDMAIController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "UObject/ConstructorHelpers.h"

UEQSDebuggerComponent::UEQSDebuggerComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // Update every 100ms

    // Initialize counters
    TotalQueries = 0;
    SuccessfulQueries = 0;
    AverageQueryTime = 0.0f;
    MatchStartTime = 0.0f;
    CurrentMatchPhase = 0;
    CurrentAIRole = TEXT("Unknown");
    CurrentTacticState = TEXT("Patrol");

    // Set default heatmap settings
    HeatmapSettings.bEnableVisualization = true;
    HeatmapSettings.SphereRadius = 50.0f;
    HeatmapSettings.DisplayDuration = 30.0f;
    HeatmapSettings.bPersistentDisplay = false;
}

void UEQSDebuggerComponent::BeginPlay()
{
    Super::BeginPlay();

    CachedWorld = GetWorld();
    MatchStartTime = CachedWorld ? CachedWorld->GetTimeSeconds() : 0.0f;

    // Try to get AI controller reference
    if (AActor* Owner = GetOwner())
    {
        CachedAIController = Cast<ATDMAIController>(Owner);
    }

    // Initialize log file if enabled
    if (bLogToFile)
    {
        FString LogPath = GetLogFilePath();
        FString LogHeader = TEXT("Timestamp,QueryType,LocationX,LocationY,LocationZ,Success,Score,AIRole,TacticState,MatchPhase\n");
        FFileHelper::SaveStringToFile(LogHeader, *LogPath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), FILEWRITE_EvenIfReadOnly);
    }
}

void UEQSDebuggerComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Generate final report
    if (bEnableDebugLogging)
    {
        GeneratePostMatchReport();
    }

    // Cleanup visualization
    CleanupHeatmapSpheres();

    // Close log file
    if (LogFileHandle.IsValid())
    {
        LogFileHandle->Close();
        LogFileHandle.Reset();
    }

    Super::EndPlay(EndPlayReason);
}

void UEQSDebuggerComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // Update current AI state
    if (CachedAIController)
    {
        // Update role and tactic state from AI controller
        // This would integrate with your AI system
        CurrentMatchPhase = FMath::FloorToInt((CachedWorld->GetTimeSeconds() - MatchStartTime) / 120.0f); // 2-minute phases
    }

    // Update heatmap visualization if enabled
    if (HeatmapSettings.bEnableVisualization && bEnableRealTimeVisualization)
    {
        UpdateHeatmapVisualization();
    }
}

void UEQSDebuggerComponent::LogEQSQuery(const FString& QueryType, const FVector& Location, bool bSuccess, 
                                       float Score, const FString& AdditionalInfo)
{
    if (!bEnableDebugLogging)
        return;

    // Create debug entry
    FEQSDebugEntry Entry;
    Entry.Timestamp = CachedWorld ? CachedWorld->GetTimeSeconds() - MatchStartTime : 0.0f;
    Entry.Location = Location;
    Entry.QueryType = QueryType;
    Entry.bSuccess = bSuccess;
    Entry.Score = Score;
    Entry.AIRole = CurrentAIRole;
    Entry.TacticState = CurrentTacticState;
    Entry.MatchPhase = CurrentMatchPhase;

    // Add to debug entries
    DebugEntries.Add(Entry);

    // Maintain max entries limit
    if (DebugEntries.Num() > MaxLogEntries)
    {
        DebugEntries.RemoveAt(0);
    }

    // Update statistics
    TotalQueries++;
    if (bSuccess)
    {
        SuccessfulQueries++;
    }

    // Write to log file
    if (bLogToFile)
    {
        WriteToLogFile(Entry);
    }

    // Real-time visualization
    if (bEnableRealTimeVisualization && CachedWorld)
    {
        FColor DebugColor = GetColorForQueryType(QueryType, bSuccess);
        
        if (bShowDebugSpheres)
        {
            DrawDebugSphere(CachedWorld, Location, HeatmapSettings.SphereRadius, 12, DebugColor, false, DebugDisplayTime);
        }

        if (bShowDebugText)
        {
            FString DebugText = FString::Printf(TEXT("%s: %s (%.2f)"), *QueryType, bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Score);
            DrawDebugString(CachedWorld, Location + FVector(0, 0, 100), DebugText, nullptr, DebugColor, DebugDisplayTime);
        }
    }

    // Log to console
    UE_LOG(LogTemp, Log, TEXT("EQS Debug: %s at %s - %s (Score: %.2f)"), 
           *QueryType, *Location.ToString(), bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Score);
}

void UEQSDebuggerComponent::LogCoverQuery(const FVector& CoverLocation, bool bSuccess, float Score)
{
    LogEQSQuery(TEXT("Cover"), CoverLocation, bSuccess, Score);
}

void UEQSDebuggerComponent::LogFlankQuery(const FVector& FlankLocation, bool bSuccess, float Score)
{
    LogEQSQuery(TEXT("Flank"), FlankLocation, bSuccess, Score);
}

void UEQSDebuggerComponent::LogReviveQuery(const FVector& ReviveLocation, bool bSuccess, float Score)
{
    LogEQSQuery(TEXT("Revive"), ReviveLocation, bSuccess, Score);
}

void UEQSDebuggerComponent::ShowHeatmap(bool bShow)
{
    HeatmapSettings.bEnableVisualization = bShow;
    
    if (bShow)
    {
        GenerateHeatmapData();
    }
    else
    {
        CleanupHeatmapSpheres();
    }
}

void UEQSDebuggerComponent::ClearHeatmap()
{
    CleanupHeatmapSpheres();
    DebugEntries.Empty();
    TotalQueries = 0;
    SuccessfulQueries = 0;
}

void UEQSDebuggerComponent::GeneratePostMatchReport()
{
    if (DebugEntries.Num() == 0)
        return;

    FString ReportPath = FPaths::ProjectSavedDir() / TEXT("EQS_Reports") / FString::Printf(TEXT("EQS_Report_%s.txt"), *GetTimestamp());
    
    FString Report = TEXT("=== EQS Debug Report ===\n\n");
    Report += FString::Printf(TEXT("Match Duration: %.2f seconds\n"), CachedWorld ? CachedWorld->GetTimeSeconds() - MatchStartTime : 0.0f);
    Report += FString::Printf(TEXT("Total Queries: %d\n"), TotalQueries);
    Report += FString::Printf(TEXT("Successful Queries: %d\n"), SuccessfulQueries);
    Report += FString::Printf(TEXT("Success Rate: %.2f%%\n\n"), TotalQueries > 0 ? (float)SuccessfulQueries / TotalQueries * 100.0f : 0.0f);

    // Query type breakdown
    TMap<FString, int32> QueryTypeCounts;
    TMap<FString, int32> QueryTypeSuccesses;
    
    for (const FEQSDebugEntry& Entry : DebugEntries)
    {
        QueryTypeCounts.FindOrAdd(Entry.QueryType)++;
        if (Entry.bSuccess)
        {
            QueryTypeSuccesses.FindOrAdd(Entry.QueryType)++;
        }
    }

    Report += TEXT("Query Type Breakdown:\n");
    for (const auto& Pair : QueryTypeCounts)
    {
        int32 Successes = QueryTypeSuccesses.FindRef(Pair.Key);
        float SuccessRate = Pair.Value > 0 ? (float)Successes / Pair.Value * 100.0f : 0.0f;
        Report += FString::Printf(TEXT("  %s: %d queries, %d successes (%.2f%%)\n"), 
                                *Pair.Key, Pair.Value, Successes, SuccessRate);
    }

    FFileHelper::SaveStringToFile(Report, *ReportPath);
    UE_LOG(LogTemp, Log, TEXT("EQS Debug Report saved to: %s"), *ReportPath);
}

void UEQSDebuggerComponent::ExportToCSV(const FString& Filename)
{
    FString CSVPath = FPaths::ProjectSavedDir() / TEXT("EQS_Exports") / FString::Printf(TEXT("%s_%s.csv"), *Filename, *GetTimestamp());
    
    FString CSVContent = TEXT("Timestamp,QueryType,LocationX,LocationY,LocationZ,Success,Score,AIRole,TacticState,MatchPhase\n");
    
    for (const FEQSDebugEntry& Entry : DebugEntries)
    {
        CSVContent += FString::Printf(TEXT("%.3f,%s,%.2f,%.2f,%.2f,%s,%.3f,%s,%s,%d\n"),
                                    Entry.Timestamp,
                                    *Entry.QueryType,
                                    Entry.Location.X,
                                    Entry.Location.Y,
                                    Entry.Location.Z,
                                    Entry.bSuccess ? TEXT("TRUE") : TEXT("FALSE"),
                                    Entry.Score,
                                    *Entry.AIRole,
                                    *Entry.TacticState,
                                    Entry.MatchPhase);
    }
    
    FFileHelper::SaveStringToFile(CSVContent, *CSVPath);
    UE_LOG(LogTemp, Log, TEXT("EQS Debug data exported to CSV: %s"), *CSVPath);
}

void UEQSDebuggerComponent::ExportToJSON(const FString& Filename)
{
    FString JSONPath = FPaths::ProjectSavedDir() / TEXT("EQS_Exports") / FString::Printf(TEXT("%s_%s.json"), *Filename, *GetTimestamp());
    
    TSharedPtr<FJsonObject> RootObject = MakeShareable(new FJsonObject);
    TArray<TSharedPtr<FJsonValue>> EntriesArray;
    
    for (const FEQSDebugEntry& Entry : DebugEntries)
    {
        TSharedPtr<FJsonObject> EntryObject = MakeShareable(new FJsonObject);
        EntryObject->SetNumberField(TEXT("timestamp"), Entry.Timestamp);
        EntryObject->SetStringField(TEXT("queryType"), Entry.QueryType);
        EntryObject->SetNumberField(TEXT("locationX"), Entry.Location.X);
        EntryObject->SetNumberField(TEXT("locationY"), Entry.Location.Y);
        EntryObject->SetNumberField(TEXT("locationZ"), Entry.Location.Z);
        EntryObject->SetBoolField(TEXT("success"), Entry.bSuccess);
        EntryObject->SetNumberField(TEXT("score"), Entry.Score);
        EntryObject->SetStringField(TEXT("aiRole"), Entry.AIRole);
        EntryObject->SetStringField(TEXT("tacticState"), Entry.TacticState);
        EntryObject->SetNumberField(TEXT("matchPhase"), Entry.MatchPhase);
        
        EntriesArray.Add(MakeShareable(new FJsonValueObject(EntryObject)));
    }
    
    RootObject->SetArrayField(TEXT("eqsEntries"), EntriesArray);
    RootObject->SetNumberField(TEXT("totalQueries"), TotalQueries);
    RootObject->SetNumberField(TEXT("successfulQueries"), SuccessfulQueries);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(RootObject.ToSharedRef(), Writer);
    
    FFileHelper::SaveStringToFile(OutputString, *JSONPath);
    UE_LOG(LogTemp, Log, TEXT("EQS Debug data exported to JSON: %s"), *JSONPath);
}

TArray<FEQSDebugEntry> UEQSDebuggerComponent::GetEntriesByType(const FString& QueryType)
{
    return DebugEntries.FilterByPredicate([QueryType](const FEQSDebugEntry& Entry)
    {
        return Entry.QueryType == QueryType;
    });
}

TArray<FEQSDebugEntry> UEQSDebuggerComponent::GetEntriesInTimeRange(float StartTime, float EndTime)
{
    return DebugEntries.FilterByPredicate([StartTime, EndTime](const FEQSDebugEntry& Entry)
    {
        return Entry.Timestamp >= StartTime && Entry.Timestamp <= EndTime;
    });
}

float UEQSDebuggerComponent::GetSuccessRateForQueryType(const FString& QueryType)
{
    TArray<FEQSDebugEntry> TypeEntries = GetEntriesByType(QueryType);
    if (TypeEntries.Num() == 0)
        return 0.0f;
    
    int32 Successes = TypeEntries.FilterByPredicate([](const FEQSDebugEntry& Entry)
    {
        return Entry.bSuccess;
    }).Num();
    
    return (float)Successes / TypeEntries.Num() * 100.0f;
}

void UEQSDebuggerComponent::WriteToLogFile(const FEQSDebugEntry& Entry)
{
    FString LogLine = FString::Printf(TEXT("%.3f,%s,%.2f,%.2f,%.2f,%s,%.3f,%s,%s,%d\n"),
                                    Entry.Timestamp,
                                    *Entry.QueryType,
                                    Entry.Location.X,
                                    Entry.Location.Y,
                                    Entry.Location.Z,
                                    Entry.bSuccess ? TEXT("TRUE") : TEXT("FALSE"),
                                    Entry.Score,
                                    *Entry.AIRole,
                                    *Entry.TacticState,
                                    Entry.MatchPhase);
    
    FString LogPath = GetLogFilePath();
    FFileHelper::SaveStringToFile(LogLine, *LogPath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), FILEWRITE_Append);
}

FString UEQSDebuggerComponent::GetLogFilePath() const
{
    return FPaths::ProjectSavedDir() / TEXT("Logs") / FString::Printf(TEXT("%s_%s.csv"), *LogFilePrefix, *GetTimestamp());
}

FString UEQSDebuggerComponent::GetTimestamp() const
{
    return FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S"));
}

FColor UEQSDebuggerComponent::GetColorForQueryType(const FString& QueryType, bool bSuccess) const
{
    if (!bSuccess)
        return HeatmapSettings.FailedColor;
    
    if (QueryType == TEXT("Cover"))
        return HeatmapSettings.CoverColor;
    else if (QueryType == TEXT("Flank"))
        return HeatmapSettings.FlankColor;
    else if (QueryType == TEXT("Revive"))
        return HeatmapSettings.ReviveColor;
    
    return HeatmapSettings.SuccessColor;
}

void UEQSDebuggerComponent::UpdateHeatmapVisualization()
{
    // Update existing spheres or create new ones based on recent entries
    // This is a simplified implementation
}

void UEQSDebuggerComponent::CleanupHeatmapSpheres()
{
    for (UStaticMeshComponent* Sphere : HeatmapSpheres)
    {
        if (IsValid(Sphere))
        {
            Sphere->DestroyComponent();
        }
    }
    HeatmapSpheres.Empty();
}

void UEQSDebuggerComponent::GenerateHeatmapData()
{
    // Generate 3D heatmap visualization
    // This would create persistent sphere components for visualization
}

// Console command implementations
void UEQSDebuggerComponent::EQSDebug_Show()
{
    ShowHeatmap(true);
}

void UEQSDebuggerComponent::EQSDebug_Hide()
{
    ShowHeatmap(false);
}

void UEQSDebuggerComponent::EQSDebug_Clear()
{
    ClearHeatmap();
}

void UEQSDebuggerComponent::EQSDebug_Export()
{
    ExportToCSV();
    ExportToJSON();
}

void UEQSDebuggerComponent::EQSDebug_Stats()
{
    if (GEngine)
    {
        FString StatsMessage = FString::Printf(TEXT("EQS Stats - Total: %d, Success: %d, Rate: %.2f%%"), 
                                             TotalQueries, SuccessfulQueries, 
                                             TotalQueries > 0 ? (float)SuccessfulQueries / TotalQueries * 100.0f : 0.0f);
        GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Yellow, StatsMessage);
    }
}

// Static utility functions
void UEQSDebuggerComponent::EnableGlobalEQSDebug(bool bEnable)
{
    // Implementation for global debug control
}

void UEQSDebuggerComponent::ClearAllDebugData()
{
    // Implementation for clearing all debug data globally
}

UEQSDebuggerComponent* UEQSDebuggerComponent::GetDebuggerForAI(AActor* AIActor)
{
    if (AIActor)
    {
        return AIActor->FindComponentByClass<UEQSDebuggerComponent>();
    }
    return nullptr;
}
