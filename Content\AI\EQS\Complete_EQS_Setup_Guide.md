# 🧠 Complete EQS Setup Guide for PUBG-Style TDM AI

This guide provides step-by-step instructions for creating all three EQS queries that support your tactical AI system.

## 🎯 Prerequisites

### 1. Enable EQS Plugin
1. Go to `Edit > Plugins > AI`
2. Enable **Environment Query System (EQS)**
3. Restart Unreal Editor

### 2. Map Requirements
- **NavMeshBoundsVolume** covering the entire map
- **Environment Query Manager** in the level
- Proper collision setup on cover objects

---

## 🔨 EQS Query 1: EQ_FindCover

### Purpose
Find tactical cover positions that provide protection from enemy fire while maintaining battlefield awareness.

### Creation Steps
1. **Right-click in Content/AI/EQS/**
2. **Artificial Intelligence > Environment Query**
3. **Name: `EQ_FindCover`**

### Generator Configuration
**Points on Circle Generator:**
- **Circle Radius**: 800 units
- **Points Count**: 24
- **Arc Direction**: Forward
- **Arc Angle**: 360 degrees
- **Context**: Querier

### Test Configuration

#### Test 1: Distance to Enemy
- **Test Purpose**: Maintain tactical distance
- **Filter Type**: Range
- **Min Value**: 300 units
- **Max Value**: 1200 units
- **Scoring**: Distance (Higher is better)
- **Weight**: 0.25

#### Test 2: Line of Sight to Enemy
- **Test Purpose**: Maintain visual contact
- **Filter Type**: Boolean
- **Line Trace**: Visibility Channel
- **From Context**: Querier
- **To Context**: Enemy Target
- **Scoring**: Boolean (True = 1.0)
- **Weight**: 0.20

#### Test 3: Cover Quality (Trace Test)
- **Test Purpose**: Ensure actual cover protection
- **Filter Type**: Boolean
- **Line Trace**: Visibility Channel
- **From Context**: Enemy Target
- **To Context**: Item
- **Scoring**: Boolean (Blocked = 1.0)
- **Weight**: 0.35

#### Test 4: Pathfinding
- **Test Purpose**: Ensure reachability
- **Filter Type**: Boolean
- **Path Mode**: Navigation Mesh
- **Scoring**: Boolean (Reachable = 1.0)
- **Weight**: 0.20

---

## 🔥 EQS Query 2: EQ_FindFlank

### Purpose
Find optimal flanking positions that provide tactical advantage over enemies.

### Creation Steps
1. **Duplicate `EQ_FindCover`**
2. **Rename to: `EQ_FindFlank`**

### Generator Configuration
**Points on Circle Generator:**
- **Circle Radius**: 1000 units
- **Points Count**: 32
- **Arc Direction**: Forward
- **Arc Angle**: 270 degrees (3/4 circle)
- **Context**: Enemy Target (not Querier!)

### Test Configuration

#### Test 1: Flanking Angle
- **Test Purpose**: Prefer side/rear positions
- **Filter Type**: Range
- **Custom Test**: Dot Product
- **Min Value**: -0.5 (90+ degrees)
- **Max Value**: 0.5
- **Scoring**: Inverse (Lower dot = better flank)
- **Weight**: 0.40

#### Test 2: Distance to Enemy
- **Test Purpose**: Optimal engagement range
- **Filter Type**: Range
- **Min Value**: 400 units
- **Max Value**: 1000 units
- **Scoring**: Inverse Distance
- **Weight**: 0.25

#### Test 3: Cover Availability
- **Test Purpose**: Ensure flanking position has cover
- **Filter Type**: Boolean
- **Line Trace**: Visibility Channel
- **Scoring**: Boolean (Has cover = 1.0)
- **Weight**: 0.25

#### Test 4: Pathfinding
- **Test Purpose**: Ensure reachability
- **Filter Type**: Boolean
- **Weight**: 0.10

---

## 🩹 EQS Query 3: EQ_ReviveSafeSpot

### Purpose
Find safe positions for reviving downed teammates with minimal enemy exposure.

### Creation Steps
1. **Duplicate `EQ_FindCover`**
2. **Rename to: `EQ_ReviveSafeSpot`**

### Generator Configuration
**Points on Circle Generator:**
- **Circle Radius**: 400 units
- **Points Count**: 16
- **Arc Direction**: Forward
- **Arc Angle**: 360 degrees
- **Context**: Revive Target

### Test Configuration

#### Test 1: Distance to Downed Ally
- **Test Purpose**: Stay close for quick revive
- **Filter Type**: Range
- **Min Value**: 50 units
- **Max Value**: 200 units
- **Scoring**: Inverse Distance (Closer = better)
- **Weight**: 0.30

#### Test 2: Cover from Enemies
- **Test Purpose**: Protection during revive
- **Filter Type**: Boolean
- **Line Trace**: Visibility Channel
- **From Context**: Known Enemies
- **To Context**: Item
- **Scoring**: Boolean (Blocked = 1.0)
- **Weight**: 0.40

#### Test 3: Distance from Enemies
- **Test Purpose**: Avoid enemy proximity
- **Filter Type**: Range
- **Min Value**: 300 units
- **Max Value**: 1000 units
- **Scoring**: Distance (Further = better)
- **Weight**: 0.20

#### Test 4: Pathfinding
- **Test Purpose**: Ensure reachability
- **Filter Type**: Boolean
- **Weight**: 0.10

---

## 🧪 Blueprint Integration

### BTTask Usage Example
```blueprint
Event Receive Execute AI
 → Run EQS Query
     - Query Template: EQ_FindCover
     - Run Mode: Best Result
     - Querier: ControlledPawn
     - Blackboard Key: CoverLocation (Vector)
 → On Success → MoveTo: CoverLocation
     - Acceptable Radius: 100
 → On Complete → Finish Execute (Success)
 → On Fail → Finish Execute (Fail)
```

### C++ Usage Example
```cpp
// In your BTTask or AI Controller
UEnvQuery* CoverQuery = LoadObject<UEnvQuery>(nullptr, TEXT("/Game/AI/EQS/EQ_FindCover"));

FEnvQueryRequest Request(CoverQuery, GetPawn());
Request.SetActorParam("EnemyTarget", CurrentTarget);
Request.Execute(EEnvQueryRunMode::SingleResult, this, 
    FQueryFinishedSignature::CreateUObject(this, &YourClass::OnQueryComplete));
```

---

## 🚀 Testing and Debugging

### Debug Commands
```
ai.DebugEQS SquadMateAI
showdebug EQS
ai.EQSTestPawn
```

### Visual Debugging
1. **Enable EQS Test Rendering** in Project Settings
2. **Show Query Results**: True
3. **Draw Radius**: 50 units
4. **Success Color**: Green
5. **Failure Color**: Red

### Performance Optimization
- **Max Results**: 5 (Cover), 3 (Flank), 3 (Revive)
- **Max Test Time**: 0.1 seconds
- **Update Frequency**: Every 2 seconds
- **Cache Duration**: 5 seconds

---

## 🔧 Advanced Configuration

### Context Parameters
- **Enemy Target**: Blackboard Key "TargetActor"
- **Revive Target**: Blackboard Key "ReviveTarget"
- **Squad Members**: Squad Manager reference
- **Known Enemies**: Perception system data

### Role-Based Adjustments
- **Scout**: Longer range, higher stealth factor
- **Assault**: Shorter range, aggressive positioning
- **Support**: Prioritize ally proximity
- **Sniper**: Elevated positions, long sight lines

### Situational Modifiers
- **Under Fire**: Use emergency parameters
- **Low Health**: Prioritize safety over aggression
- **Team Coordination**: Factor in ally positions

This complete EQS setup provides your PUBG-style TDM AI with intelligent spatial reasoning for cover, flanking, and revive operations!
