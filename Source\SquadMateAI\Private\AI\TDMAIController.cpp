#include "AI/TDMAIController.h"
#include "AI/SquadManager.h"
#include "Characters/VictorCharacter.h"
#include "Weapons/TDMWeaponSystem.h"
#include "Components/DecisionLoggerComponent.h"
#include "Components/HealthComponent.h"
#include "Components/InventoryComponent.h"
#include "Components/SquadRoleComponent.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "EnvironmentQuery/EnvQuery.h"
#include "EnvironmentQuery/EnvQueryManager.h"
#include "GameFramework/Character.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "HAL/FileManager.h"

ATDMAIController::ATDMAIController()
{
    PrimaryActorTick.bCanEverTick = true;

    // Initialize AI Components
    AIPerceptionComponent = CreateDefaultSubobject<UAIPerceptionComponent>(TEXT("AIPerceptionComponent"));
    BehaviorTreeComponent = CreateDefaultSubobject<UBehaviorTreeComponent>(TEXT("BehaviorTreeComponent"));
    BlackboardComponent = CreateDefaultSubobject<UBlackboardComponent>(TEXT("BlackboardComponent"));
    DecisionLogger = CreateDefaultSubobject<UDecisionLoggerComponent>(TEXT("DecisionLogger"));
    WeaponSystem = CreateDefaultSubobject<UTDMWeaponSystem>(TEXT("WeaponSystem"));

    // Set default values
    CurrentTactic = ETDMTacticState::Patrol;
    AssignedRole = ETDMRole::Assault;
    CurrentLane = ETDMLane::Center;
    TeamID = 0;
    KillCount = 0;
    DeathCount = 0;
    AccuracyPercentage = 0.0f;
    AverageReactionTime = 0.0f;
    TacticalEfficiency = 0.0f;
    LastCombatTime = 0.0f;
    LastDecisionTime = 0.0f;
    SpawnProtectionEndTime = 0.0f;

    // Initialize Blackboard Keys
    TargetActorKey = FName("TargetActor");
    TacticStateKey = FName("TacticState");
    HasLineOfSightKey = FName("HasLineOfSight");
    IsInCombatKey = FName("IsInCombat");
    CoverLocationKey = FName("CoverLocation");
    TeamMatesKey = FName("TeamMates");
    EnemiesKey = FName("Enemies");
    CurrentLaneKey = FName("CurrentLane");
    IsUnderFireKey = FName("IsUnderFire");
    AmmoLowKey = FName("AmmoLow");
    HealthLowKey = FName("HealthLow");
    ReviveTargetKey = FName("ReviveTarget");
    IsRevivingKey = FName("IsReviving");
    SpawnProtectedKey = FName("SpawnProtected");

    // Set default JSON configuration path
    JSONConfigurationPath = TEXT("Content/AI/Configurations/TDM_AI_Config.json");
}

void ATDMAIController::BeginPlay()
{
    Super::BeginPlay();
    
    InitializeAIComponents();
    LoadDefaultConfiguration();
    
    // Set up decision making timer
    GetWorld()->GetTimerManager().SetTimer(
        FTimerHandle(),
        this,
        &ATDMAIController::UpdateDecisionMaking,
        0.1f, // High frequency for TDM responsiveness
        true
    );
}

void ATDMAIController::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    UpdateBlackboardValues();
    UpdateCombatState();
    UpdateTeamCoordination();
    HandleSpawnProtection();
    UpdateInfiniteAmmo();
}

void ATDMAIController::OnPossess(APawn* InPawn)
{
    Super::OnPossess(InPawn);
    
    if (TDMBehaviorTree && BlackboardComponent)
    {
        BlackboardComponent->InitializeBlackboard(*TDMBehaviorTree->BlackboardAsset);
        BehaviorTreeComponent->StartTree(*TDMBehaviorTree);
    }

    // Initialize spawn protection
    SpawnProtectionEndTime = GetWorld()->GetTimeSeconds() + 3.0f; // 3 second spawn protection
}

void ATDMAIController::InitializeAIComponents()
{
    InitializePerception();
    InitializeBlackboard();
    StartBehaviorTree();
}

void ATDMAIController::InitializePerception()
{
    if (!AIPerceptionComponent)
        return;

    // Configure Sight Sense for TDM
    UAISenseConfig_Sight* SightConfig = CreateDefaultSubobject<UAISenseConfig_Sight>(TEXT("SightConfig"));
    SightConfig->SightRadius = 3000.0f; // Extended for warehouse map
    SightConfig->LoseSightRadius = 3500.0f;
    SightConfig->PeripheralVisionAngleDegrees = 120.0f; // Wide FOV for TDM
    SightConfig->DetectionByAffiliation.bNeutral = true;
    SightConfig->DetectionByAffiliation.bFriendly = true;
    SightConfig->DetectionByAffiliation.bEnemy = true;
    SightConfig->AutoSuccessRangeFromLastSeenLocation = 500.0f;

    // Configure Hearing Sense for gunfire detection
    UAISenseConfig_Hearing* HearingConfig = CreateDefaultSubobject<UAISenseConfig_Hearing>(TEXT("HearingConfig"));
    HearingConfig->HearingRange = 2000.0f;
    HearingConfig->DetectionByAffiliation.bNeutral = true;
    HearingConfig->DetectionByAffiliation.bFriendly = true;
    HearingConfig->DetectionByAffiliation.bEnemy = true;

    AIPerceptionComponent->ConfigureSense(*SightConfig);
    AIPerceptionComponent->ConfigureSense(*HearingConfig);
    AIPerceptionComponent->SetDominantSense(SightConfig->GetSenseImplementation());

    // Bind perception events
    AIPerceptionComponent->OnPerceptionUpdated.AddDynamic(this, &ATDMAIController::OnPerceptionUpdated);
}

void ATDMAIController::InitializeBlackboard()
{
    if (!BlackboardComponent)
        return;

    // Initialize all blackboard values to defaults
    BlackboardComponent->SetValueAsObject(TargetActorKey, nullptr);
    BlackboardComponent->SetValueAsEnum(TacticStateKey, static_cast<uint8>(CurrentTactic));
    BlackboardComponent->SetValueAsBool(HasLineOfSightKey, false);
    BlackboardComponent->SetValueAsBool(IsInCombatKey, false);
    BlackboardComponent->SetValueAsVector(CoverLocationKey, FVector::ZeroVector);
    BlackboardComponent->SetValueAsEnum(CurrentLaneKey, static_cast<uint8>(CurrentLane));
    BlackboardComponent->SetValueAsBool(IsUnderFireKey, false);
    BlackboardComponent->SetValueAsBool(AmmoLowKey, false);
    BlackboardComponent->SetValueAsBool(HealthLowKey, false);
    BlackboardComponent->SetValueAsObject(ReviveTargetKey, nullptr);
    BlackboardComponent->SetValueAsBool(IsRevivingKey, false);
    BlackboardComponent->SetValueAsBool(SpawnProtectedKey, true);
}

void ATDMAIController::StartBehaviorTree()
{
    if (TDMBehaviorTree && BehaviorTreeComponent)
    {
        BehaviorTreeComponent->StartTree(*TDMBehaviorTree);
    }
}

void ATDMAIController::LoadDefaultConfiguration()
{
    // Load JSON configuration if available
    if (!JSONConfigurationPath.IsEmpty())
    {
        LoadJSONConfiguration(JSONConfigurationPath);
    }
    else
    {
        // Create default decision tree for PUBGM TDM
        FTDMDecisionNode ImmediateThreat;
        ImmediateThreat.NodeName = "immediate_threat_response";
        ImmediateThreat.Priority = 1;
        ImmediateThreat.Conditions = {"enemy_visible", "being_shot_at"};
        ImmediateThreat.Actions = {"return_fire", "seek_cover", "call_enemy_position"};
        ImmediateThreat.CooldownTime = 0.1f;

        FTDMDecisionNode AggressiveEngagement;
        AggressiveEngagement.NodeName = "aggressive_engagement";
        AggressiveEngagement.Priority = 2;
        AggressiveEngagement.Conditions = {"enemy_spotted", "health_above_50", "ammo_sufficient"};
        AggressiveEngagement.Actions = {"engage_target", "coordinate_with_team", "control_recoil"};
        AggressiveEngagement.CooldownTime = 0.2f;

        FTDMDecisionNode TacticalPositioning;
        TacticalPositioning.NodeName = "tactical_positioning";
        TacticalPositioning.Priority = 3;
        TacticalPositioning.Conditions = {"no_immediate_threat", "zone_control_needed"};
        TacticalPositioning.Actions = {"move_to_strategic_position", "watch_angles", "support_team"};
        TacticalPositioning.CooldownTime = 1.0f;

        DecisionTree.Add(ImmediateThreat);
        DecisionTree.Add(AggressiveEngagement);
        DecisionTree.Add(TacticalPositioning);
    }
}

void ATDMAIController::UpdateDecisionMaking(float DeltaTime)
{
    if (GetWorld()->GetTimeSeconds() - LastDecisionTime < 0.1f)
        return;

    ETDMTacticState NewTactic = DetermineBestTactic();
    
    if (NewTactic != CurrentTactic)
    {
        ETDMTacticState OldTactic = CurrentTactic;
        SetTacticState(NewTactic);
        ExecuteTactic(NewTactic);
        
        // Broadcast state change
        OnTacticStateChanged.Broadcast(OldTactic, NewTactic);
        
        if (DecisionLogger)
        {
            FString TacticName = UEnum::GetValueAsString(NewTactic);
            DecisionLogger->LogDecision(TacticName, GetPawn()->GetActorLocation(), true);
        }
    }
    
    LastDecisionTime = GetWorld()->GetTimeSeconds();
}

ETDMTacticState ATDMAIController::DetermineBestTactic()
{
    // Process decision tree based on PUBGM TDM priorities
    for (const FTDMDecisionNode& Node : DecisionTree)
    {
        // Check cooldown
        if (ActionCooldowns.Contains(Node.NodeName))
        {
            float LastUsed = ActionCooldowns[Node.NodeName];
            if (GetWorld()->GetTimeSeconds() - LastUsed < Node.CooldownTime)
                continue;
        }

        // Evaluate all conditions
        bool AllConditionsMet = true;
        for (const FString& Condition : Node.Conditions)
        {
            if (!EvaluateCondition(Condition))
            {
                AllConditionsMet = false;
                break;
            }
        }

        if (AllConditionsMet)
        {
            // Execute actions
            for (const FString& Action : Node.Actions)
            {
                ExecuteAction(Action);
            }

            // Update cooldown
            ActionCooldowns.Add(Node.NodeName, GetWorld()->GetTimeSeconds());

            // Return appropriate tactic state
            if (Node.NodeName.Contains("threat") || Node.NodeName.Contains("engagement"))
                return ETDMTacticState::Engage;
            else if (Node.NodeName.Contains("positioning"))
                return ETDMTacticState::Hold;
            else if (Node.NodeName.Contains("flank"))
                return ETDMTacticState::Flank;
        }
    }

    // Default fallback
    return ETDMTacticState::Patrol;
}

void ATDMAIController::ExecuteTactic(ETDMTacticState Tactic)
{
    // Tactic execution is primarily handled by Behavior Tree
    // This method handles immediate state changes and parameter adjustments
    
    switch (Tactic)
    {
        case ETDMTacticState::Engage:
            // Set aggressive parameters
            if (WeaponSystem)
            {
                WeaponSystem->SetFireMode(true);
                WeaponSystem->SetAimAssist(true);
            }
            break;
            
        case ETDMTacticState::Retreat:
            // Set defensive parameters
            if (WeaponSystem)
            {
                WeaponSystem->SetFireMode(false);
            }
            break;
            
        case ETDMTacticState::Flank:
            // Set flanking parameters
            break;
            
        case ETDMTacticState::Suppress:
            // Set suppressive fire parameters
            if (WeaponSystem)
            {
                WeaponSystem->SetSuppressiveMode(true);
            }
            break;
            
        default:
            break;
    }
}

bool ATDMAIController::EvaluateCondition(const FString& Condition)
{
    if (Condition == "enemy_visible")
    {
        return HasTarget();
    }
    else if (Condition == "being_shot_at")
    {
        return BlackboardComponent->GetValueAsBool(IsUnderFireKey);
    }
    else if (Condition == "enemy_spotted")
    {
        return BlackboardComponent->GetValueAsObject(TargetActorKey) != nullptr;
    }
    else if (Condition == "health_above_50")
    {
        if (UHealthComponent* HealthComp = GetPawn()->FindComponentByClass<UHealthComponent>())
        {
            return HealthComp->GetHealthPercentage() > 0.5f;
        }
    }
    else if (Condition == "ammo_sufficient")
    {
        return !BlackboardComponent->GetValueAsBool(AmmoLowKey);
    }
    else if (Condition == "no_immediate_threat")
    {
        return !BlackboardComponent->GetValueAsBool(IsUnderFireKey) && !HasTarget();
    }
    else if (Condition == "zone_control_needed")
    {
        // Check if team needs to control specific lanes
        return true; // Simplified for now
    }

    return false;
}

void ATDMAIController::ExecuteAction(const FString& Action)
{
    if (Action == "return_fire")
    {
        if (WeaponSystem && HasTarget())
        {
            WeaponSystem->StartFiring();
        }
    }
    else if (Action == "seek_cover")
    {
        // Trigger cover-seeking behavior
        SetTacticState(ETDMTacticState::Hold);
    }
    else if (Action == "call_enemy_position")
    {
        // Communicate enemy position to team
        if (AActor* Target = GetCurrentTarget())
        {
            // Squad communication logic here
        }
    }
    else if (Action == "engage_target")
    {
        SetTacticState(ETDMTacticState::Engage);
    }
    else if (Action == "coordinate_with_team")
    {
        // Team coordination logic
    }
    else if (Action == "control_recoil")
    {
        if (WeaponSystem)
        {
            WeaponSystem->EnableRecoilControl(true);
        }
    }
}

// EQS Dynamic Selection Implementation
void ATDMAIController::DecideAndRunEQS()
{
    APawn* Pawn = GetPawn();
    if (!Pawn) return;

    FString TacticName = UEnum::GetValueAsString(CurrentTactic);

    if (CurrentTactic == ETDMTacticState::Engage || CurrentTactic == ETDMTacticState::Flank)
    {
        RunFlankQuery();
    }
    else if (CurrentTactic == ETDMTacticState::Revive)
    {
        RunReviveQuery();
    }
    else if (CurrentTactic == ETDMTacticState::Hold || CurrentTactic == ETDMTacticState::Retreat)
    {
        RunCoverQuery();
    }
    else
    {
        // Default to cover query for patrol and other states
        RunCoverQuery();
    }
}

void ATDMAIController::RunCoverQuery()
{
    APawn* Pawn = GetPawn();
    if (!Pawn) return;

    UEnvQuery* QueryToRun = nullptr;

    // Check if under fire for emergency cover
    bool bIsUnderFire = BlackboardComponent && BlackboardComponent->GetValueAsBool(IsUnderFireKey);
    if (bIsUnderFire && EQS_EmergencyCover)
    {
        QueryToRun = EQS_EmergencyCover;
    }
    else if (EQS_FindCover)
    {
        QueryToRun = EQS_FindCover;
    }

    if (QueryToRun)
    {
        FEnvQueryRequest Request(QueryToRun, Pawn);

        // Set query parameters based on current situation
        AActor* CurrentTarget = GetCurrentTarget();
        if (CurrentTarget)
        {
            Request.SetActorParam("EnemyTarget", CurrentTarget);
        }

        Request.Execute(EEnvQueryRunMode::SingleResult, this,
            FQueryFinishedSignature::CreateUObject(this, &ATDMAIController::HandleCoverQueryResult));
    }
}

void ATDMAIController::RunFlankQuery()
{
    APawn* Pawn = GetPawn();
    if (!Pawn || !EQS_FindFlank) return;

    AActor* CurrentTarget = GetCurrentTarget();
    if (!CurrentTarget) return;

    FEnvQueryRequest Request(EQS_FindFlank, Pawn);
    Request.SetActorParam("EnemyTarget", CurrentTarget);

    // Set role-based parameters
    Request.SetFloatParam("FlankDistance", AssignedRole == ETDMRole::Flanker ? 800.0f : 600.0f);
    Request.SetFloatParam("StealthFactor", AssignedRole == ETDMRole::Flanker ? 0.8f : 0.5f);

    Request.Execute(EEnvQueryRunMode::SingleResult, this,
        FQueryFinishedSignature::CreateUObject(this, &ATDMAIController::HandleFlankQueryResult));
}

void ATDMAIController::RunReviveQuery()
{
    APawn* Pawn = GetPawn();
    if (!Pawn || !EQS_ReviveSafe) return;

    // Check if we have a revive target
    AActor* ReviveTarget = nullptr;
    if (BlackboardComponent)
    {
        ReviveTarget = Cast<AActor>(BlackboardComponent->GetValueAsObject(ReviveTargetKey));
    }

    if (!ReviveTarget) return;

    FEnvQueryRequest Request(EQS_ReviveSafe, Pawn);
    Request.SetActorParam("ReviveTarget", ReviveTarget);

    // Set safety parameters based on current threat level
    bool bIsUnderFire = BlackboardComponent && BlackboardComponent->GetValueAsBool(IsUnderFireKey);
    Request.SetFloatParam("SafetyRadius", bIsUnderFire ? 400.0f : 300.0f);
    Request.SetFloatParam("MaxReviveDistance", bIsUnderFire ? 500.0f : 800.0f);

    Request.Execute(EEnvQueryRunMode::SingleResult, this,
        FQueryFinishedSignature::CreateUObject(this, &ATDMAIController::HandleReviveQueryResult));
}

// EQS Result Handlers
void ATDMAIController::HandleCoverQueryResult(TSharedPtr<FEnvQueryResult> Result)
{
    if (Result.IsValid() && Result->IsSuccessful() && Result->Items.Num() > 0)
    {
        FVector CoverLocation = Result->GetItemAsLocation(0);

        if (BlackboardComponent)
        {
            BlackboardComponent->SetValueAsVector(CoverLocationKey, CoverLocation);

            // Log the decision
            if (DecisionLogger)
            {
                DecisionLogger->LogDecision("EQS_CoverFound", CoverLocation, true);
            }
        }

        // Move to cover location
        MoveToLocation(CoverLocation, 100.0f);
    }
    else
    {
        // EQS failed, use fallback behavior
        if (DecisionLogger)
        {
            DecisionLogger->LogDecision("EQS_CoverFailed", GetPawn()->GetActorLocation(), false);
        }
    }
}

void ATDMAIController::HandleFlankQueryResult(TSharedPtr<FEnvQueryResult> Result)
{
    if (Result.IsValid() && Result->IsSuccessful() && Result->Items.Num() > 0)
    {
        FVector FlankLocation = Result->GetItemAsLocation(0);

        if (BlackboardComponent)
        {
            BlackboardComponent->SetValueAsVector("FlankLocation", FlankLocation);

            // Log the decision
            if (DecisionLogger)
            {
                DecisionLogger->LogDecision("EQS_FlankFound", FlankLocation, true);
            }
        }

        // Move to flank position
        MoveToLocation(FlankLocation, 150.0f);
    }
    else
    {
        // Fallback to cover seeking if flanking fails
        RunCoverQuery();
    }
}

void ATDMAIController::HandleReviveQueryResult(TSharedPtr<FEnvQueryResult> Result)
{
    if (Result.IsValid() && Result->IsSuccessful() && Result->Items.Num() > 0)
    {
        FVector ReviveLocation = Result->GetItemAsLocation(0);

        if (BlackboardComponent)
        {
            BlackboardComponent->SetValueAsVector("ReviveLocation", ReviveLocation);

            // Log the decision
            if (DecisionLogger)
            {
                DecisionLogger->LogDecision("EQS_ReviveSpotFound", ReviveLocation, true);
            }
        }

        // Move to safe revive position
        MoveToLocation(ReviveLocation, 80.0f);
    }
    else
    {
        // Revive query failed, abort revive attempt
        if (BlackboardComponent)
        {
            BlackboardComponent->SetValueAsObject(ReviveTargetKey, nullptr);
        }

        // Switch to defensive tactic
        SetTacticState(ETDMTacticState::Hold);
    }
    else if (Action == "move_to_strategic_position")
    {
        // Strategic positioning logic
    }
    else if (Action == "watch_angles")
    {
        SetTacticState(ETDMTacticState::Hold);
    }
    else if (Action == "support_team")
    {
        // Support behavior logic
    }
}

// Public Interface Implementation
void ATDMAIController::SetTacticState(ETDMTacticState NewState)
{
    CurrentTactic = NewState;
    if (BlackboardComponent)
    {
        BlackboardComponent->SetValueAsEnum(TacticStateKey, static_cast<uint8>(NewState));
    }
}

void ATDMAIController::SetRole(ETDMRole NewRole)
{
    AssignedRole = NewRole;
    
    // Adjust loadout based on role
    switch (NewRole)
    {
        case ETDMRole::Assault:
            CurrentLoadout.PrimaryWeapon = ETDMWeaponType::AR_M416;
            CurrentLoadout.SecondaryWeapon = ETDMWeaponType::SMG_UMP45;
            break;
        case ETDMRole::Support:
            CurrentLoadout.PrimaryWeapon = ETDMWeaponType::AR_SCAR;
            CurrentLoadout.SecondaryWeapon = ETDMWeaponType::SMG_VECTOR;
            break;
        case ETDMRole::Sniper:
            CurrentLoadout.PrimaryWeapon = ETDMWeaponType::SNIPER_KAR98;
            CurrentLoadout.SecondaryWeapon = ETDMWeaponType::AR_AKM;
            break;
        case ETDMRole::Flanker:
            CurrentLoadout.PrimaryWeapon = ETDMWeaponType::SMG_UZI;
            CurrentLoadout.SecondaryWeapon = ETDMWeaponType::SHOTGUN_S12K;
            break;
        case ETDMRole::Entry:
            CurrentLoadout.PrimaryWeapon = ETDMWeaponType::SHOTGUN_S12K;
            CurrentLoadout.SecondaryWeapon = ETDMWeaponType::SMG_VECTOR;
            break;
    }
    
    if (WeaponSystem)
    {
        WeaponSystem->SwitchLoadout(CurrentLoadout);
    }
}

void ATDMAIController::SetLane(ETDMLane NewLane)
{
    CurrentLane = NewLane;
    if (BlackboardComponent)
    {
        BlackboardComponent->SetValueAsEnum(CurrentLaneKey, static_cast<uint8>(NewLane));
    }
}

void ATDMAIController::LoadJSONConfiguration(const FString& ConfigPath)
{
    FString JSONString;
    if (FFileHelper::LoadFileToString(JSONString, *ConfigPath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JSONString);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Parse decision tree
            if (JsonObject->HasField("decision_tree"))
            {
                TSharedPtr<FJsonObject> DecisionTreeObj = JsonObject->GetObjectField("decision_tree");
                if (DecisionTreeObj->HasField("nodes"))
                {
                    TArray<TSharedPtr<FJsonValue>> NodesArray = DecisionTreeObj->GetArrayField("nodes");
                    DecisionTree.Empty();

                    for (auto& NodeValue : NodesArray)
                    {
                        TSharedPtr<FJsonObject> NodeObj = NodeValue->AsObject();
                        FTDMDecisionNode Node;

                        Node.NodeName = NodeObj->GetStringField("name");
                        Node.Priority = NodeObj->GetIntegerField("priority");
                        Node.CooldownTime = NodeObj->GetNumberField("cooldown");

                        // Parse conditions
                        TArray<TSharedPtr<FJsonValue>> ConditionsArray = NodeObj->GetArrayField("conditions");
                        for (auto& ConditionValue : ConditionsArray)
                        {
                            Node.Conditions.Add(ConditionValue->AsString());
                        }

                        // Parse actions
                        TArray<TSharedPtr<FJsonValue>> ActionsArray = NodeObj->GetArrayField("actions");
                        for (auto& ActionValue : ActionsArray)
                        {
                            Node.Actions.Add(ActionValue->AsString());
                        }

                        DecisionTree.Add(Node);
                    }
                }
            }

            // Parse loadout configurations
            if (JsonObject->HasField("loadouts"))
            {
                TSharedPtr<FJsonObject> LoadoutsObj = JsonObject->GetObjectField("loadouts");
                // Process loadout configurations based on role
            }
        }
    }
}

void ATDMAIController::SwitchLoadout(const FTDMLoadout& NewLoadout)
{
    CurrentLoadout = NewLoadout;
    if (WeaponSystem)
    {
        WeaponSystem->SwitchLoadout(NewLoadout);
    }
}

void ATDMAIController::OnKill(AActor* Victim)
{
    KillCount++;
    OnKillConfirmed.Broadcast(Victim);

    if (DecisionLogger)
    {
        DecisionLogger->LogKill(Victim, GetPawn()->GetActorLocation());
    }

    // Update tactical efficiency
    TacticalEfficiency = CalculateTacticalEfficiency();
}

void ATDMAIController::OnDeath(AActor* Killer)
{
    DeathCount++;
    OnDeathEvent.Broadcast(Killer);

    if (DecisionLogger)
    {
        DecisionLogger->LogDeath(Killer, GetPawn()->GetActorLocation());
    }

    // Reset current state
    SetTacticState(ETDMTacticState::Patrol);
    BlackboardComponent->SetValueAsObject(TargetActorKey, nullptr);
}

void ATDMAIController::OnRespawn()
{
    // Reset spawn protection
    SpawnProtectionEndTime = GetWorld()->GetTimeSeconds() + 3.0f;
    BlackboardComponent->SetValueAsBool(SpawnProtectedKey, true);

    // Reset tactical state
    SetTacticState(ETDMTacticState::Patrol);

    // Refresh weapon system
    if (WeaponSystem)
    {
        WeaponSystem->RefreshAmmo();
        WeaponSystem->SwitchLoadout(CurrentLoadout);
    }
}

bool ATDMAIController::IsInCombat() const
{
    return (GetWorld()->GetTimeSeconds() - LastCombatTime) < 5.0f;
}

bool ATDMAIController::HasTarget() const
{
    return BlackboardComponent && BlackboardComponent->GetValueAsObject(TargetActorKey) != nullptr;
}

AActor* ATDMAIController::GetCurrentTarget() const
{
    if (BlackboardComponent)
    {
        return Cast<AActor>(BlackboardComponent->GetValueAsObject(TargetActorKey));
    }
    return nullptr;
}

void ATDMAIController::UpdateBlackboardValues()
{
    if (!BlackboardComponent)
        return;

    // Update combat state
    bool bInCombat = IsInCombat();
    BlackboardComponent->SetValueAsBool(IsInCombatKey, bInCombat);

    // Update tactic state
    BlackboardComponent->SetValueAsEnum(TacticStateKey, static_cast<uint8>(CurrentTactic));

    // Update lane information
    BlackboardComponent->SetValueAsEnum(CurrentLaneKey, static_cast<uint8>(CurrentLane));

    // Update spawn protection status
    bool bSpawnProtected = GetWorld()->GetTimeSeconds() < SpawnProtectionEndTime;
    BlackboardComponent->SetValueAsBool(SpawnProtectedKey, bSpawnProtected);

    // Update health status
    if (UHealthComponent* HealthComp = GetPawn()->FindComponentByClass<UHealthComponent>())
    {
        bool bHealthLow = HealthComp->GetHealthPercentage() < 0.3f;
        BlackboardComponent->SetValueAsBool(HealthLowKey, bHealthLow);
    }

    // Update ammo status (always false for TDM infinite ammo)
    BlackboardComponent->SetValueAsBool(AmmoLowKey, false);
}

void ATDMAIController::UpdateCombatState()
{
    // Check if under fire
    bool bUnderFire = false;
    if (AIPerceptionComponent)
    {
        TArray<AActor*> PerceivedActors;
        AIPerceptionComponent->GetCurrentlyPerceivedActors(nullptr, PerceivedActors);

        for (AActor* Actor : PerceivedActors)
        {
            // Check if any enemy is aiming at us
            if (IsEnemyAimingAtUs(Actor))
            {
                bUnderFire = true;
                LastCombatTime = GetWorld()->GetTimeSeconds();
                break;
            }
        }
    }

    if (BlackboardComponent)
    {
        BlackboardComponent->SetValueAsBool(IsUnderFireKey, bUnderFire);
    }
}

void ATDMAIController::UpdateTeamCoordination()
{
    // Update team member locations and status
    TArray<AActor*> TeamMates;
    TArray<AActor*> Enemies;

    if (AIPerceptionComponent)
    {
        TArray<AActor*> PerceivedActors;
        AIPerceptionComponent->GetCurrentlyPerceivedActors(nullptr, PerceivedActors);

        for (AActor* Actor : PerceivedActors)
        {
            if (ACharacter* Character = Cast<ACharacter>(Actor))
            {
                // Determine if teammate or enemy based on team ID
                if (IsTeammate(Character))
                {
                    TeamMates.Add(Actor);
                }
                else
                {
                    Enemies.Add(Actor);
                }
            }
        }
    }

    // Update blackboard with team information
    if (BlackboardComponent)
    {
        // Store team and enemy arrays (simplified - in production use proper array handling)
        if (Enemies.Num() > 0)
        {
            BlackboardComponent->SetValueAsObject(TargetActorKey, Enemies[0]);
        }
    }
}

void ATDMAIController::ProcessEnemyDetection(AActor* Enemy)
{
    if (!Enemy)
        return;

    // Set as target if we don't have one or this is closer
    AActor* CurrentTarget = GetCurrentTarget();
    if (!CurrentTarget || GetDistanceToActor(Enemy) < GetDistanceToActor(CurrentTarget))
    {
        BlackboardComponent->SetValueAsObject(TargetActorKey, Enemy);
        LastCombatTime = GetWorld()->GetTimeSeconds();

        OnEnemyDetected.Broadcast(Enemy);
    }
}

void ATDMAIController::ProcessAllyDetection(AActor* Ally)
{
    if (!Ally)
        return;

    // Check if ally needs reviving
    if (UHealthComponent* AllyHealth = Ally->FindComponentByClass<UHealthComponent>())
    {
        if (AllyHealth->IsDown() && !AllyHealth->IsDead())
        {
            BlackboardComponent->SetValueAsObject(ReviveTargetKey, Ally);
        }
    }
}

void ATDMAIController::OnPerceptionUpdated(const TArray<AActor*>& UpdatedActors)
{
    for (AActor* Actor : UpdatedActors)
    {
        if (!Actor)
            continue;

        if (ACharacter* Character = Cast<ACharacter>(Actor))
        {
            if (IsTeammate(Character))
            {
                ProcessAllyDetection(Actor);
            }
            else
            {
                ProcessEnemyDetection(Actor);
            }
        }
    }
}

// Combat Logic Implementation
bool ATDMAIController::CanEngageTarget(AActor* Target) const
{
    if (!Target)
        return false;

    // Check if we have line of sight
    if (!BlackboardComponent->GetValueAsBool(HasLineOfSightKey))
        return false;

    // Check if we're not under spawn protection
    if (BlackboardComponent->GetValueAsBool(SpawnProtectedKey))
        return false;

    // Check distance (effective range for TDM)
    float Distance = GetDistanceToActor(Target);
    return Distance <= 2000.0f; // Max effective range in warehouse
}

bool ATDMAIController::ShouldRetreat() const
{
    // Retreat if health is critically low
    if (UHealthComponent* HealthComp = GetPawn()->FindComponentByClass<UHealthComponent>())
    {
        if (HealthComp->GetHealthPercentage() < 0.2f)
            return true;
    }

    // Retreat if overwhelmed (multiple enemies)
    TArray<AActor*> PerceivedEnemies;
    if (AIPerceptionComponent)
    {
        TArray<AActor*> AllPerceived;
        AIPerceptionComponent->GetCurrentlyPerceivedActors(nullptr, AllPerceived);

        for (AActor* Actor : AllPerceived)
        {
            if (!IsTeammate(Cast<ACharacter>(Actor)))
            {
                PerceivedEnemies.Add(Actor);
            }
        }
    }

    return PerceivedEnemies.Num() >= 3; // Retreat if facing 3+ enemies
}

bool ATDMAIController::ShouldSeekCover() const
{
    return BlackboardComponent->GetValueAsBool(IsUnderFireKey) ||
           BlackboardComponent->GetValueAsBool(HealthLowKey);
}

bool ATDMAIController::ShouldFlank() const
{
    // Flank if we have a target but no clear shot
    if (HasTarget() && !BlackboardComponent->GetValueAsBool(HasLineOfSightKey))
        return true;

    // Flank if assigned flanker role
    if (AssignedRole == ETDMRole::Flanker)
        return true;

    return false;
}

bool ATDMAIController::ShouldReviveAlly() const
{
    AActor* ReviveTarget = Cast<AActor>(BlackboardComponent->GetValueAsObject(ReviveTargetKey));
    if (!ReviveTarget)
        return false;

    // Don't revive if under fire
    if (BlackboardComponent->GetValueAsBool(IsUnderFireKey))
        return false;

    // Check if we're close enough and safe
    float Distance = GetDistanceToActor(ReviveTarget);
    return Distance <= 300.0f; // Revive range
}

bool ATDMAIController::ShouldSuppressFire() const
{
    // Suppress if support role and teammates are advancing
    return AssignedRole == ETDMRole::Support && HasTarget();
}

bool ATDMAIController::ShouldPeek() const
{
    // Peek if we have cover and a target
    return HasTarget() && BlackboardComponent->GetValueAsVector(CoverLocationKey) != FVector::ZeroVector;
}

// PUBGM Specific Mechanics
void ATDMAIController::HandleSpawnProtection()
{
    if (GetWorld()->GetTimeSeconds() >= SpawnProtectionEndTime)
    {
        if (BlackboardComponent->GetValueAsBool(SpawnProtectedKey))
        {
            BlackboardComponent->SetValueAsBool(SpawnProtectedKey, false);
        }
    }
}

void ATDMAIController::UpdateInfiniteAmmo()
{
    // PUBGM TDM has infinite ammo - ensure weapon system reflects this
    if (WeaponSystem)
    {
        WeaponSystem->RefreshAmmo();
    }
}

void ATDMAIController::ProcessSlideMovement()
{
    // Implement slide mechanics for aggressive movement
    if (CurrentTactic == ETDMTacticState::Engage || CurrentTactic == ETDMTacticState::Flank)
    {
        // Trigger slide movement in character
        if (ACharacter* Character = Cast<ACharacter>(GetPawn()))
        {
            // Slide implementation would go here
        }
    }
}

void ATDMAIController::HandlePeekMechanics()
{
    // Implement peek mechanics for cover-based combat
    if (ShouldPeek())
    {
        SetTacticState(ETDMTacticState::Peek);
    }
}

void ATDMAIController::UpdateLaneControl()
{
    // Update lane assignment based on team strategy
    // This would integrate with squad manager for coordinated lane control
}

// Utility Helper Functions
float ATDMAIController::GetDistanceToActor(AActor* Actor) const
{
    if (!Actor || !GetPawn())
        return FLT_MAX;

    return FVector::Dist(GetPawn()->GetActorLocation(), Actor->GetActorLocation());
}

bool ATDMAIController::IsTeammate(ACharacter* Character) const
{
    if (!Character)
        return false;

    // Implementation depends on your team system
    // For now, assume characters have a team ID component or property
    return false; // Placeholder
}

bool ATDMAIController::IsEnemyAimingAtUs(AActor* Enemy) const
{
    if (!Enemy)
        return false;

    // Check if enemy is aiming in our direction
    // This would require access to enemy's aim direction
    return false; // Placeholder
}

float ATDMAIController::CalculateTacticalEfficiency() const
{
    // Calculate efficiency based on K/D ratio, accuracy, and objective completion
    float KDRatio = GetKDRatio();
    float EfficiencyScore = (KDRatio * 0.4f) + (AccuracyPercentage * 0.3f) + (TacticalEfficiency * 0.3f);
    return FMath::Clamp(EfficiencyScore, 0.0f, 100.0f);
}
