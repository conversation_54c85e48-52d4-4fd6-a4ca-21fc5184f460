# 🎭 AnimBP Tactical AI Guide - Peek, Shoot, Crouch Fire

## 🎯 **Purpose**
Complete Animation Blueprint setup for PUBG-style tactical AI with peek mechanics, crouch firing, and combat animations.

---

## 📋 **Step 1: Create Animation Blueprint**

### **Setup:**
1. **Right-click in Content/Characters/Victor/Animations/**
2. **Animation → Animation Blueprint**
3. **Parent Class**: AnimInstance
4. **Target Skeleton**: Victor_Skeleton
5. **Name**: `ABP_TacticalAI`

---

## 🎛️ **Step 2: Animation Variables**

### **Movement Variables:**
```cpp
// Basic Movement
bool IsMoving = false;
float MovementSpeed = 0.0f;
float MovementDirection = 0.0f;
FVector Velocity = FVector::ZeroVector;

// Movement States
bool IsRunning = false;
bool IsWalking = false;
bool IsSprinting = false;
```

### **Combat Variables:**
```cpp
// Aiming & Shooting
bool IsAiming = false;
bool IsShooting = false;
bool IsReloading = false;
bool HasWeapon = true;

// Weapon Types
EWeaponType CurrentWeaponType = EWeaponType::AssaultRifle;
bool IsAutomatic = true;
```

### **Tactical Variables:**
```cpp
// Stance Control
bool IsCrouching = false;
bool IsProne = false;
bool IsStanding = true;

// Peek Mechanics
bool IsPeekingLeft = false;
bool IsPeekingRight = false;
float PeekAmount = 0.0f; // -1.0 to 1.0
float LeanAngle = 0.0f;

// Cover System
bool IsInCover = false;
bool IsLeaningLeft = false;
bool IsLeaningRight = false;
```

### **AI State Variables:**
```cpp
// AI Behavior
ETacticState CurrentTactic = ETacticState::Patrol;
bool IsEngaging = false;
bool IsFlanking = false;
bool IsReviving = false;
bool IsUnderFire = false;
```

---

## 🎬 **Step 3: Animation State Machine**

### **Main State Machine: "Locomotion"**

#### **States:**

##### **1. Idle State**
```
Entry Conditions: Speed <= 10 && !IsCrouching && !IsProne
Animations: Idle_Rifle, Idle_Pistol (based on weapon)
Transitions:
├── To Walking: Speed > 10
├── To Crouching: IsCrouching == true
└── To Combat: IsAiming == true
```

##### **2. Walking State**
```
Entry Conditions: Speed > 10 && Speed <= 300
Animations: Walk_Forward, Walk_Backward, Walk_Strafe
Blend Space: 2D (Speed, Direction)
Transitions:
├── To Idle: Speed <= 10
├── To Running: Speed > 300
└── To Combat: IsAiming == true
```

##### **3. Running State**
```
Entry Conditions: Speed > 300 && !IsAiming
Animations: Run_Forward, Run_Backward, Run_Strafe
Blend Space: 2D (Speed, Direction)
Transitions:
├── To Walking: Speed <= 300
├── To Combat: IsAiming == true
└── To Sprinting: Speed > 500
```

##### **4. Crouching State**
```
Entry Conditions: IsCrouching == true
Sub-States:
├── CrouchIdle: Speed <= 10
├── CrouchWalk: Speed > 10 && Speed <= 200
└── CrouchAim: IsAiming == true
```

##### **5. Combat State**
```
Entry Conditions: IsAiming == true || IsShooting == true
Sub-States:
├── AimIdle: IsAiming && !IsShooting
├── AimWalk: IsAiming && IsMoving
├── Firing: IsShooting == true
└── Reloading: IsReloading == true
```

---

## 🎯 **Step 4: Peek & Lean System**

### **Peek State Machine: "PeekSystem"**

#### **States:**

##### **1. Normal State**
```
Entry Conditions: !IsPeekingLeft && !IsPeekingRight
Animation: Normal stance
```

##### **2. Peek Left State**
```
Entry Conditions: IsPeekingLeft == true
Animation: PeekLeft_Idle, PeekLeft_Aim, PeekLeft_Fire
Blend: Based on IsAiming and IsShooting
```

##### **3. Peek Right State**
```
Entry Conditions: IsPeekingRight == true
Animation: PeekRight_Idle, PeekRight_Aim, PeekRight_Fire
Blend: Based on IsAiming and IsShooting
```

### **Lean Bone Modification:**
```cpp
// In AnimBP Event Graph
Event Blueprint Update Animation
 ↓
[Get Pawn Owner]
 ↓
[Cast to TacticalAI Character]
 ↓
[Get Lean Amount] → Set LeanAngle
 ↓
[Modify Bone: Spine_02]
 ├── Rotation: (0, 0, LeanAngle)
 └── Translation: (0, LeanAngle * 10, 0)
```

---

## 🔫 **Step 5: Combat Animation Layers**

### **Layer 1: Upper Body Combat**
```
Slot: UpperBody
Animations:
├── Aim_Rifle_Idle
├── Aim_Rifle_Fire
├── Aim_Pistol_Idle
├── Aim_Pistol_Fire
├── Reload_Rifle
└── Reload_Pistol
```

### **Layer 2: Additive Peek**
```
Slot: Additive
Animations:
├── Additive_PeekLeft
├── Additive_PeekRight
├── Additive_LeanLeft
└── Additive_LeanRight
```

### **Layer 3: Full Body Actions**
```
Slot: FullBody
Animations:
├── Revive_Ally
├── Throw_Grenade
├── Melee_Attack
└── Take_Cover
```

---

## 🎮 **Step 6: Blueprint Integration**

### **Event Graph: Update Variables**

```blueprint
[Event Blueprint Update Animation]
 ↓
[Try Get Pawn Owner]
 ↓
[Cast to Victor Character]
 ↓
[Sequence: Update All Variables]
 ├── [Get Velocity] → Set MovementSpeed
 ├── [Get Is Crouching] → Set IsCrouching
 ├── [Get Is Aiming] → Set IsAiming
 ├── [Get Current Weapon] → Set CurrentWeaponType
 ├── [Get Tactic State] → Set CurrentTactic
 └── [Get Peek State] → Set IsPeekingLeft/Right
```

### **Combat State Updates:**
```blueprint
[Get AI Controller]
 ↓
[Cast to TDMAIController]
 ↓
[Get Blackboard Component]
 ↓
[Parallel Execution]
 ├── [Get BB Value: IsUnderFire] → Set IsUnderFire
 ├── [Get BB Value: IsShooting] → Set IsShooting
 ├── [Get BB Value: IsReloading] → Set IsReloading
 └── [Get BB Value: TacticState] → Set CurrentTactic
```

---

## 🎭 **Step 7: Advanced Animation Features**

### **Procedural Aiming:**
```cpp
// Aim Offset Blend Space
Horizontal Input: AimYaw (-90 to 90)
Vertical Input: AimPitch (-45 to 45)

Animations:
├── AimCenter (0, 0)
├── AimLeft (-90, 0)
├── AimRight (90, 0)
├── AimUp (0, 45)
└── AimDown (0, -45)
```

### **Weapon Sway:**
```blueprint
[Get World Delta Seconds]
 ↓
[Sine Wave] (Frequency: 0.5, Amplitude: 2.0)
 ↓
[Apply to Weapon Bone]
 └── Rotation: (SineValue, 0, 0)
```

### **Recoil Animation:**
```blueprint
[On Weapon Fire Event]
 ↓
[Play Animation Montage: Recoil_Rifle]
 ├── Blend In: 0.1s
 ├── Blend Out: 0.3s
 └── Play Rate: Based on Fire Rate
```

---

## 🎯 **Step 8: Montage System**

### **Combat Montages:**

#### **Fire Montage:**
```
Sections:
├── SingleShot (0.2s)
├── BurstFire (0.6s)
└── FullAuto (Loop)

Blend Settings:
├── Blend In: 0.05s
└── Blend Out: 0.1s
```

#### **Reload Montage:**
```
Sections:
├── ReloadStart (0.5s)
├── ReloadLoop (1.0s)
└── ReloadEnd (0.5s)

Notifies:
├── EjectMag (0.3s)
├── InsertMag (1.2s)
└── CockWeapon (1.8s)
```

#### **Peek Fire Montage:**
```
Sections:
├── PeekOut (0.3s)
├── Fire (0.2s)
└── PeekIn (0.3s)

Blend Settings:
├── Additive: True
└── Slot: UpperBody
```

---

## 🔧 **Step 9: AI Integration**

### **From C++ AI Controller:**
```cpp
// Set animation states
if (UAnimInstance* AnimInstance = Character->GetMesh()->GetAnimInstance())
{
    AnimInstance->SetBool("IsCrouching", bShouldCrouch);
    AnimInstance->SetBool("IsAiming", bIsAiming);
    AnimInstance->SetBool("IsPeekingLeft", bPeekLeft);
    AnimInstance->SetBool("IsShooting", bIsFiring);
    AnimInstance->SetFloat("PeekAmount", PeekValue);
}
```

### **From Behavior Tree:**
```blueprint
[BTTask: Peek and Shoot]
 ↓
[Set Blackboard Bool: IsPeeking = true]
 ↓
[Play Animation Montage: PeekFire]
 ↓
[Wait for Montage Complete]
 ↓
[Set Blackboard Bool: IsPeeking = false]
```

---

## 🎨 **Step 10: Visual Polish**

### **IK (Inverse Kinematics):**
```cpp
// Foot IK for uneven terrain
Two Bone IK Nodes:
├── LeftFoot_IK
└── RightFoot_IK

// Hand IK for weapon grip
Two Bone IK Nodes:
├── LeftHand_IK (Weapon Grip)
└── RightHand_IK (Weapon Support)
```

### **Look At System:**
```cpp
// Head tracking for awareness
Look At Node:
├── Target: Current Enemy
├── Bone: Head
├── Clamp: 45 degrees
└── Interpolation Speed: 5.0
```

This comprehensive AnimBP setup provides **realistic tactical animations** that integrate seamlessly with your PUBG-style AI behavior system!
