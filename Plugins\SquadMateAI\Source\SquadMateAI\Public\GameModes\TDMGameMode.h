#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "Engine/DataTable.h"
#include "TDMGameMode.generated.h"

// Team information structure
USTRUCT(BlueprintType)
struct FTDMTeamInfo : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    int32 TeamID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    FString TeamName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    FColor TeamColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    int32 Score;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    int32 Kills;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    int32 Deaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    TArray<class APawn*> TeamMembers;

    FTDMTeamInfo()
    {
        TeamID = 0;
        TeamName = TEXT("Team");
        TeamColor = FColor::White;
        Score = 0;
        Kills = 0;
        Deaths = 0;
    }
};

// Match statistics
USTRUCT(BlueprintType)
struct FTDMMatchStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Stats")
    float MatchDuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Stats")
    int32 TotalKills;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Stats")
    int32 TotalDeaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Stats")
    float AverageKillDistance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Stats")
    FString WinningTeam;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Stats")
    int32 WinningScore;

    FTDMMatchStats()
    {
        MatchDuration = 0.0f;
        TotalKills = 0;
        TotalDeaths = 0;
        AverageKillDistance = 0.0f;
        WinningTeam = TEXT("None");
        WinningScore = 0;
    }
};

// Game phase enumeration
UENUM(BlueprintType)
enum class ETDMGamePhase : uint8
{
    WaitingToStart  UMETA(DisplayName = "Waiting to Start"),
    InProgress      UMETA(DisplayName = "In Progress"),
    Overtime        UMETA(DisplayName = "Overtime"),
    Finished        UMETA(DisplayName = "Finished")
};

/**
 * Team Deathmatch Game Mode for SquadMate AI
 * Manages 5v5 TDM matches with AI agents, scoring, and match flow
 */
UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ATDMGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    ATDMGameMode();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void Tick(float DeltaTime) override;

    // === MATCH MANAGEMENT ===
    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void StartMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void EndMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RestartMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void PauseMatch(bool bPause);

    // === TEAM MANAGEMENT ===
    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void SpawnAITeams();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void SpawnAIAgent(int32 TeamID, const FVector& SpawnLocation);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void AssignPlayerToTeam(APawn* Player, int32 TeamID);

    // === SCORING ===
    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RegisterKill(APawn* Killer, APawn* Victim, const FString& WeaponType, float Distance);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RegisterDeath(APawn* Victim, const FString& CauseOfDeath);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void AddTeamScore(int32 TeamID, int32 Points);

    // === GETTERS ===
    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    FTDMTeamInfo GetTeamInfo(int32 TeamID) const;

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    TArray<FTDMTeamInfo> GetAllTeams() const;

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    ETDMGamePhase GetCurrentGamePhase() const { return CurrentGamePhase; }

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    float GetMatchTimeRemaining() const;

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    FTDMMatchStats GetMatchStats() const;

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    int32 GetTeamIDForPawn(APawn* Pawn) const;

    // === CONFIGURATION ===
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    float MatchDuration = 600.0f; // 10 minutes

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    int32 ScoreLimit = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    int32 TeamSize = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    int32 NumberOfTeams = 2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    bool bEnableOvertime = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    float OvertimeDuration = 120.0f; // 2 minutes

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    bool bAutoSpawnAI = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Settings")
    float RespawnDelay = 5.0f;

    // === AI CONFIGURATION ===
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    TSubclassOf<class APawn> AIAgentClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    TSubclassOf<class AController> AIControllerClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    TArray<FVector> Team1SpawnPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    TArray<FVector> Team2SpawnPoints;

    // === EVENTS ===
    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FGenericTeamAgentEvent OnMatchStarted;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FGenericTeamAgentEvent OnMatchEnded;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FGenericTeamAgentEvent OnKillRegistered;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FGenericTeamAgentEvent OnScoreChanged;

protected:
    // === INTERNAL STATE ===
    UPROPERTY()
    TMap<int32, FTDMTeamInfo> Teams;

    UPROPERTY()
    ETDMGamePhase CurrentGamePhase;

    UPROPERTY()
    float MatchStartTime;

    UPROPERTY()
    float MatchEndTime;

    UPROPERTY()
    bool bMatchPaused;

    UPROPERTY()
    FTDMMatchStats CurrentMatchStats;

    // === SPAWN MANAGEMENT ===
    UPROPERTY()
    TArray<class APlayerStart*> CachedPlayerStarts;

    // === HELPER FUNCTIONS ===
    void InitializeTeams();
    void UpdateMatchTimer(float DeltaTime);
    void CheckWinConditions();
    void HandleMatchEnd();
    void SpawnAIAgentAtLocation(int32 TeamID, const FVector& Location);
    FVector GetRandomSpawnLocation(int32 TeamID);
    void UpdateMatchStats();
    void BroadcastMatchEvents();

    // === RESPAWN SYSTEM ===
    UFUNCTION()
    void HandlePawnDeath(APawn* DeadPawn);

    UFUNCTION()
    void RespawnPawn(APawn* DeadPawn);

    // === TIMER HANDLES ===
    FTimerHandle MatchTimerHandle;
    FTimerHandle StatsUpdateHandle;
    TMap<APawn*, FTimerHandle> RespawnTimers;

public:
    // === CONSOLE COMMANDS ===
    UFUNCTION(Exec)
    void TDM_StartMatch();

    UFUNCTION(Exec)
    void TDM_EndMatch();

    UFUNCTION(Exec)
    void TDM_SpawnAI(int32 Count = 10);

    UFUNCTION(Exec)
    void TDM_ShowStats();

    UFUNCTION(Exec)
    void TDM_SetScore(int32 TeamID, int32 Score);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FGenericTeamAgentEvent, APawn*, Pawn);
};
