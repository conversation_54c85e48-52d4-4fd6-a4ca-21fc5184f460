{"FileVersion": 3, "Version": 1, "VersionName": "1.0.0", "FriendlyName": "SquadMate AI", "Description": "Complete PUBG-style Team Deathmatch AI system with EQS, tactical behavior trees, and advanced combat mechanics", "Category": "AI", "CreatedBy": "Augment Code", "CreatedByURL": "https://augmentcode.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "SquadMateAI", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine", "Core", "CoreUObject", "AIModule", "GameplayTasks", "NavigationSystem", "UMG", "Slate", "SlateCore"]}, {"Name": "SquadMateAIEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["SquadMateAI", "UnrealEd", "ToolMenus", "EditorStyle", "EditorWidgets"]}], "Plugins": [{"Name": "AISupport", "Enabled": true}, {"Name": "EnvironmentQueryEditor", "Enabled": true}, {"Name": "GameplayTasks", "Enabled": true}, {"Name": "NavigationSystem", "Enabled": true}]}