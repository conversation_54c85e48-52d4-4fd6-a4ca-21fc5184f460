#include "AI/BTTask_FlankEnemy.h"
#include "AI/TDMAIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "EnvironmentQuery/EnvQuery.h"
#include "EnvironmentQuery/EnvQueryManager.h"
#include "AIController.h"
#include "NavigationSystem.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Kismet/KismetMathLibrary.h"
#include "Animation/AnimInstance.h"
#include "GameFramework/Character.h"
#include "Components/CapsuleComponent.h"
#include "TimerManager.h"

UBTTask_FlankEnemy::UBTTask_FlankEnemy()
{
    NodeName = "Flank Enemy (Advanced)";
    bNotifyTick = true;
    bNotifyTaskFinished = true;
    bCreateNodeInstance = true;

    // Initialize default blackboard keys
    TargetActorKey.SelectedKeyName = "TargetActor";
    FlankLocationKey.SelectedKeyName = "FlankLocation";
    SquadRoleKey.SelectedKeyName = "SquadRole";
    IsFlankingKey.SelectedKeyName = "IsFlanking";

    // Configure stealth parameters
    StealthParams.MovementSpeed = 250.0f;
    StealthParams.bUseStealthMovement = true;
    StealthParams.bMaintainCover = true;
    StealthParams.PreEngagementDelay = 1.0f;

    // Configure aggressive parameters
    AggressiveParams.MovementSpeed = 500.0f;
    AggressiveParams.bUseStealthMovement = false;
    AggressiveParams.bMaintainCover = false;
    AggressiveParams.PreEngagementDelay = 0.2f;
}

EBTNodeResult::Type UBTTask_FlankEnemy::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    APawn* ControlledPawn = AIController ? AIController->GetPawn() : nullptr;

    if (!AIController || !ControlledPawn)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: Invalid AI Controller or Pawn"));
        return EBTNodeResult::Failed;
    }

    // Initialize task memory
    InitializeTaskMemory(NodeMemory);
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);

    // Check if we have a valid target
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        TaskMemory->TargetEnemy = Cast<AActor>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
    }

    if (!TaskMemory->TargetEnemy)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: No valid target for flanking"));
        return EBTNodeResult::Failed;
    }

    // Set flanking state in blackboard
    if (BlackboardComp)
    {
        BlackboardComp->SetValueAsBool(IsFlankingKey.SelectedKeyName, true);
    }

    // Broadcast flank intent to team
    if (bBroadcastFlankIntent)
    {
        BroadcastFlankIntent(OwnerComp, FVector::ZeroVector); // Position will be set after EQS
    }

    // Start the flanking sequence
    StartFlankingSequence(OwnerComp, NodeMemory);

    return EBTNodeResult::InProgress;
}

void UBTTask_FlankEnemy::StartFlankingSequence(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    
    TaskMemory->TaskStartTime = GetWorld()->GetTimeSeconds();
    TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Planning);
    
    // Execute EQS query to find flank position
    ExecuteFlankQuery(OwnerComp, NodeMemory);
}

void UBTTask_FlankEnemy::ExecuteFlankQuery(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    APawn* ControlledPawn = AIController->GetPawn();
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);

    // Select appropriate query based on role and situation
    UEnvQuery* QueryToUse = SelectOptimalFlankQuery(OwnerComp);
    if (!QueryToUse)
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: No valid EQS query found"));
        TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Failed);
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Create and configure the query request
    FEnvQueryRequest QueryRequest(QueryToUse, ControlledPawn);
    
    // Set query parameters
    FFlankExecutionParams CurrentParams = GetAdjustedParams(OwnerComp);
    QueryRequest.SetActorParam("EnemyTarget", TaskMemory->TargetEnemy);
    QueryRequest.SetFloatParam("FlankDistance", CurrentParams.EngagementRange);
    QueryRequest.SetFloatParam("MovementSpeed", CurrentParams.MovementSpeed);

    // Execute the query
    TaskMemory->bEQSQueryActive = true;
    QueryRequest.Execute(EEnvQueryRunMode::SingleResult, AIController,
        FQueryFinishedSignature::CreateUObject(this, &UBTTask_FlankEnemy::OnFlankQueryComplete, &OwnerComp, NodeMemory));
}

void UBTTask_FlankEnemy::OnFlankQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp, uint8* NodeMemory)
{
    if (!OwnerComp || !Result.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: Invalid query result"));
        if (OwnerComp)
        {
            TransitionToPhase(*OwnerComp, NodeMemory, EFlankPhase::Failed);
            FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
        }
        return;
    }

    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    TaskMemory->bEQSQueryActive = false;

    if (Result->IsSuccessful() && Result->Items.Num() > 0)
    {
        // Get the best flank position
        FVector FlankPosition = Result->GetItemAsLocation(0);
        
        // Validate the position
        if (ValidateFlankPosition(FlankPosition, *OwnerComp))
        {
            TaskMemory->FlankPosition = FlankPosition;
            
            // Store in blackboard
            UBlackboardComponent* BlackboardComp = OwnerComp->GetBlackboardComponent();
            if (BlackboardComp)
            {
                BlackboardComp->SetValueAsVector(FlankLocationKey.SelectedKeyName, FlankPosition);
            }

            // Debug visualization
            if (AAIController* AIController = OwnerComp->GetAIOwner())
            {
                DrawDebugSphere(AIController->GetWorld(), FlankPosition, 120.0f, 12, FColor::Orange, false, 10.0f);
                DrawDebugLine(AIController->GetWorld(), FlankPosition, TaskMemory->TargetEnemy->GetActorLocation(), 
                            FColor::Red, false, 10.0f, 0, 3.0f);
            }

            // Update team about flank position
            BroadcastFlankIntent(*OwnerComp, FlankPosition);

            // Transition to movement phase
            TransitionToPhase(*OwnerComp, NodeMemory, EFlankPhase::Moving);
            InitiateFlankMovement(*OwnerComp, NodeMemory);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: Flank position validation failed"));
            TransitionToPhase(*OwnerComp, NodeMemory, EFlankPhase::Failed);
            FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: EQS query failed to find valid flank position"));
        TransitionToPhase(*OwnerComp, NodeMemory, EFlankPhase::Failed);
        FinishLatentTask(*OwnerComp, EBTNodeResult::Failed);
    }
}

void UBTTask_FlankEnemy::InitiateFlankMovement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    FFlankExecutionParams CurrentParams = GetAdjustedParams(OwnerComp);

    if (AIController)
    {
        // Set movement parameters
        TaskMemory->bMovementActive = true;
        
        // Play flank animation if configured
        PlayFlankAnimation(OwnerComp, CurrentParams);
        
        // Set crouch state if required
        if (bUseCrouchMovement)
        {
            SetCrouchState(OwnerComp, true);
        }

        // Request cover fire if configured
        if (bRequestCoverFire)
        {
            RequestTeamCoverFire(OwnerComp);
        }

        // Start movement to flank position
        AIController->MoveToLocation(TaskMemory->FlankPosition, CurrentParams.AcceptableRadius, 
                                   CurrentParams.bUseStealthMovement);
    }
}

EBTNodeResult::Type UBTTask_FlankEnemy::AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    CleanupFlankingTask(OwnerComp, NodeMemory);
    return EBTNodeResult::Aborted;
}

void UBTTask_FlankEnemy::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    
    // Check if target is still valid
    if (!IsTargetStillValid(OwnerComp, NodeMemory))
    {
        TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Failed);
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Handle current phase
    switch (TaskMemory->CurrentPhase)
    {
        case EFlankPhase::Moving:
            UpdateMovementPhase(OwnerComp, NodeMemory, DeltaSeconds);
            break;
            
        case EFlankPhase::Positioning:
            // Check if positioning is complete
            if (GetWorld()->GetTimeSeconds() - TaskMemory->PhaseStartTime > 1.0f)
            {
                TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Engaging);
                InitiateEngagement(OwnerComp, NodeMemory);
            }
            break;
            
        case EFlankPhase::Engaging:
            HandleCombatPhase(OwnerComp, NodeMemory, DeltaSeconds);
            break;
            
        case EFlankPhase::Completed:
            NotifyFlankCompletion(OwnerComp, true);
            FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
            break;
            
        case EFlankPhase::Failed:
            NotifyFlankCompletion(OwnerComp, false);
            FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
            break;
    }

    // Check for timeout
    FFlankExecutionParams CurrentParams = GetAdjustedParams(OwnerComp);
    if (GetWorld()->GetTimeSeconds() - TaskMemory->TaskStartTime > CurrentParams.FlankTimeout)
    {
        HandlePhaseTimeout(OwnerComp, NodeMemory);
    }
}

void UBTTask_FlankEnemy::UpdateMovementPhase(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    FFlankExecutionParams CurrentParams = GetAdjustedParams(OwnerComp);

    if (!AIController || !AIController->GetPawn())
        return;

    // Check if we've reached the flank position
    float DistanceToFlank = FVector::Dist(AIController->GetPawn()->GetActorLocation(), TaskMemory->FlankPosition);
    
    if (DistanceToFlank <= CurrentParams.AcceptableRadius)
    {
        OnFlankPositionReached(OwnerComp, NodeMemory);
    }
    else if (!AIController->GetPathFollowingComponent()->HasReached(TaskMemory->FlankPosition))
    {
        // Still moving, check if path is valid
        if (AIController->GetMoveStatus() == EPathFollowingStatus::Idle)
        {
            // Movement failed, try again or abort
            UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: Movement to flank position failed"));
            TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Failed);
        }
    }
}

void UBTTask_FlankEnemy::OnFlankPositionReached(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    
    TaskMemory->bMovementActive = false;
    TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Positioning);
    
    // Face the target
    if (AAIController* AIController = OwnerComp.GetAIOwner())
    {
        if (TaskMemory->TargetEnemy)
        {
            FVector LookDirection = TaskMemory->TargetEnemy->GetActorLocation() - AIController->GetPawn()->GetActorLocation();
            LookDirection.Z = 0.0f;
            LookDirection.Normalize();
            
            FRotator TargetRotation = LookDirection.Rotation();
            AIController->GetPawn()->SetActorRotation(TargetRotation);
        }
    }
}

void UBTTask_FlankEnemy::InitiateEngagement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    FFlankExecutionParams CurrentParams = GetAdjustedParams(OwnerComp);
    
    TaskMemory->bEngagementActive = true;
    
    // Wait for pre-engagement delay
    if (CurrentParams.PreEngagementDelay > 0.0f)
    {
        GetWorld()->GetTimerManager().SetTimer(
            TaskMemory->EngagementTimer,
            [this, &OwnerComp, NodeMemory]()
            {
                ExecutePeekAndShoot(OwnerComp, NodeMemory);
            },
            CurrentParams.PreEngagementDelay,
            false
        );
    }
    else
    {
        ExecutePeekAndShoot(OwnerComp, NodeMemory);
    }
}

void UBTTask_FlankEnemy::ExecutePeekAndShoot(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);
    
    // Check if we have clear shot
    if (HasClearShotToTarget(OwnerComp, TaskMemory->FlankPosition))
    {
        // Play peek fire animation
        if (bUsePeekMechanics)
        {
            PlayPeekFireAnimation(OwnerComp);
        }
        
        // Engage target (this would integrate with your weapon system)
        if (AAIController* AIController = OwnerComp.GetAIOwner())
        {
            if (ATDMAIController* TDMController = Cast<ATDMAIController>(AIController))
            {
                // This would call your weapon system to fire
                // TDMController->GetWeaponSystem()->StartFiring();
            }
        }
        
        // Mark flanking as successful after brief engagement
        GetWorld()->GetTimerManager().SetTimer(
            TaskMemory->EngagementTimer,
            [this, &OwnerComp, NodeMemory]()
            {
                TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Completed);
            },
            2.0f,
            false
        );
    }
    else
    {
        // No clear shot, flanking failed
        TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Failed);
    }
}

void UBTTask_FlankEnemy::HandleCombatPhase(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    // Combat phase handling - integrate with your weapon system
    // This is where the AI would continue engaging the target
}

UEnvQuery* UBTTask_FlankEnemy::SelectOptimalFlankQuery(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
        return FlankQuery;

    ETDMSquadRole CurrentRole = static_cast<ETDMSquadRole>(BlackboardComp->GetValueAsEnum(SquadRoleKey.SelectedKeyName));

    switch (CurrentRole)
    {
        case ETDMSquadRole::Scout:
            return StealthFlankQuery ? StealthFlankQuery : FlankQuery;
        case ETDMSquadRole::Assault:
            return AggressiveFlankQuery ? AggressiveFlankQuery : FlankQuery;
        default:
            return FlankQuery;
    }
}

FFlankExecutionParams UBTTask_FlankEnemy::GetAdjustedParams(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
        return DefaultParams;

    ETDMSquadRole CurrentRole = static_cast<ETDMSquadRole>(BlackboardComp->GetValueAsEnum(SquadRoleKey.SelectedKeyName));

    // Check for role-specific parameters
    if (RoleBasedParams.Contains(CurrentRole))
    {
        return RoleBasedParams[CurrentRole];
    }

    // Select based on role
    switch (CurrentRole)
    {
        case ETDMSquadRole::Scout:
            return StealthParams;
        case ETDMSquadRole::Assault:
            return AggressiveParams;
        default:
            return DefaultParams;
    }
}

bool UBTTask_FlankEnemy::ValidateFlankPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController || !AIController->GetPawn())
        return false;

    // Check if position is reachable
    UNavigationSystemV1* NavSys = UNavigationSystemV1::GetCurrent(AIController->GetWorld());
    if (NavSys)
    {
        FNavLocation NavLocation;
        if (!NavSys->ProjectPointToNavigation(Position, NavLocation, FVector(100.0f, 100.0f, 100.0f)))
        {
            return false;
        }
    }

    return true;
}

bool UBTTask_FlankEnemy::IsTargetStillValid(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);

    if (!TaskMemory->TargetEnemy || !IsValid(TaskMemory->TargetEnemy))
        return false;

    // Check if target is still alive and within reasonable range
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (AIController && AIController->GetPawn())
    {
        float DistanceToTarget = FVector::Dist(AIController->GetPawn()->GetActorLocation(),
                                             TaskMemory->TargetEnemy->GetActorLocation());
        return DistanceToTarget <= 2000.0f; // Max engagement range
    }

    return false;
}

bool UBTTask_FlankEnemy::HasClearShotToTarget(UBehaviorTreeComponent& OwnerComp, const FVector& Position)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(OwnerComp.GetNodeMemory(this, OwnerComp.GetActiveNode()));

    if (!TaskMemory->TargetEnemy)
        return false;

    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController)
        return false;

    // Perform line trace to check for clear shot
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(AIController->GetPawn());

    bool bHit = AIController->GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Position,
        TaskMemory->TargetEnemy->GetActorLocation(),
        ECC_Visibility,
        QueryParams
    );

    return !bHit || HitResult.GetActor() == TaskMemory->TargetEnemy;
}

void UBTTask_FlankEnemy::TransitionToPhase(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EFlankPhase NewPhase)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);

    TaskMemory->CurrentPhase = NewPhase;
    TaskMemory->PhaseStartTime = GetWorld()->GetTimeSeconds();

    UE_LOG(LogTemp, Log, TEXT("BTTask_FlankEnemy: Transitioning to phase %d"), (int32)NewPhase);
}

void UBTTask_FlankEnemy::HandlePhaseTimeout(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    UE_LOG(LogTemp, Warning, TEXT("BTTask_FlankEnemy: Task timed out"));
    TransitionToPhase(OwnerComp, NodeMemory, EFlankPhase::Failed);
}

void UBTTask_FlankEnemy::CleanupFlankingTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);

    // Clear timers
    if (TaskMemory->TimeoutTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(TaskMemory->TimeoutTimer);
    }
    if (TaskMemory->EngagementTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(TaskMemory->EngagementTimer);
    }

    // Clear flanking state in blackboard
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (BlackboardComp)
    {
        BlackboardComp->SetValueAsBool(IsFlankingKey.SelectedKeyName, false);
    }

    // Reset crouch state
    SetCrouchState(OwnerComp, false);
}

UBTTask_FlankEnemy::FBTTask_FlankEnemyMemory* UBTTask_FlankEnemy::GetFlankMemory(uint8* NodeMemory) const
{
    return reinterpret_cast<FBTTask_FlankEnemyMemory*>(NodeMemory);
}

void UBTTask_FlankEnemy::InitializeTaskMemory(uint8* NodeMemory) const
{
    FBTTask_FlankEnemyMemory* TaskMemory = GetFlankMemory(NodeMemory);

    TaskMemory->CurrentPhase = EFlankPhase::Planning;
    TaskMemory->FlankPosition = FVector::ZeroVector;
    TaskMemory->TargetEnemy = nullptr;
    TaskMemory->TaskStartTime = 0.0f;
    TaskMemory->PhaseStartTime = 0.0f;
    TaskMemory->bEQSQueryActive = false;
    TaskMemory->bMovementActive = false;
    TaskMemory->bEngagementActive = false;
}

// Animation and team coordination stubs - implement based on your systems
void UBTTask_FlankEnemy::PlayFlankAnimation(UBehaviorTreeComponent& OwnerComp, const FFlankExecutionParams& Params)
{
    // Implement animation logic here
}

void UBTTask_FlankEnemy::PlayPeekFireAnimation(UBehaviorTreeComponent& OwnerComp)
{
    // Implement peek fire animation here
}

void UBTTask_FlankEnemy::SetCrouchState(UBehaviorTreeComponent& OwnerComp, bool bCrouch)
{
    // Implement crouch state logic here
}

void UBTTask_FlankEnemy::BroadcastFlankIntent(UBehaviorTreeComponent& OwnerComp, const FVector& FlankPosition)
{
    // Implement team communication here
}

void UBTTask_FlankEnemy::RequestTeamCoverFire(UBehaviorTreeComponent& OwnerComp)
{
    // Implement cover fire request here
}

void UBTTask_FlankEnemy::NotifyFlankCompletion(UBehaviorTreeComponent& OwnerComp, bool bSuccess)
{
    // Implement completion notification here
}

void UBTTask_FlankEnemy::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
    CleanupFlankingTask(OwnerComp, NodeMemory);
    Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_FlankEnemy::GetStaticDescription() const
{
    return FString::Printf(TEXT("Advanced Flank Enemy\nQuery: %s\nStealth: %s\nAggressive: %s"),
        FlankQuery ? *FlankQuery->GetName() : TEXT("None"),
        StealthFlankQuery ? *StealthFlankQuery->GetName() : TEXT("None"),
        AggressiveFlankQuery ? *AggressiveFlankQuery->GetName() : TEXT("None"));
}

// Static utility functions
bool UBTTask_FlankEnemy::IsValidFlankPosition(const FVector& FlankPos, const FVector& EnemyPos,
                                            const FVector& EnemyForward, float MinAngle)
{
    FVector ToFlank = (FlankPos - EnemyPos).GetSafeNormal();
    float DotProduct = FVector::DotProduct(EnemyForward, ToFlank);
    float Angle = FMath::RadiansToDegrees(FMath::Acos(DotProduct));

    return Angle >= MinAngle;
}

float UBTTask_FlankEnemy::CalculateFlankAdvantage(const FVector& FlankPos, const FVector& EnemyPos,
                                                const FVector& EnemyForward)
{
    FVector ToFlank = (FlankPos - EnemyPos).GetSafeNormal();
    float DotProduct = FVector::DotProduct(EnemyForward, ToFlank);

    // Convert to advantage score (behind enemy = 1.0, in front = 0.0)
    return FMath::Clamp((1.0f - DotProduct) * 0.5f, 0.0f, 1.0f);
}
