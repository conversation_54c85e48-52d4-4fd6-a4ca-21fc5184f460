#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "EnvironmentQuery/EnvQueryTypes.h"
#include "EQSDebuggerComponent.generated.h"

// EQS debug entry for logging and visualization
USTRUCT(BlueprintType)
struct FEQSDebugEntry : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    float Timestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    FVector Location;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    FString QueryType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    bool bSuccess;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    float Score;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    FString AIRole;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    FString TacticState;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Entry")
    int32 MatchPhase;

    FEQSDebugEntry()
    {
        Timestamp = 0.0f;
        Location = FVector::ZeroVector;
        QueryType = TEXT("Unknown");
        bSuccess = false;
        Score = 0.0f;
        AIRole = TEXT("Unknown");
        TacticState = TEXT("Unknown");
        MatchPhase = 0;
    }
};

// Heatmap visualization settings
USTRUCT(BlueprintType)
struct FEQSHeatmapSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    bool bEnableVisualization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    float SphereRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    float DisplayDuration = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    bool bPersistentDisplay = false;

    // Color coding for different query types
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor CoverColor = FColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor FlankColor = FColor::Orange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor ReviveColor = FColor::Purple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor FailedColor = FColor::Red;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor SuccessColor = FColor::Green;
};

/**
 * Component for debugging and visualizing EQS query results
 * Provides post-match heatmap analysis and real-time debugging
 */
UCLASS(BlueprintType, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class SQUADMATEAI_API UEQSDebuggerComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UEQSDebuggerComponent();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Debug logging functions
    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void LogEQSQuery(const FString& QueryType, const FVector& Location, bool bSuccess, 
                    float Score = 0.0f, const FString& AdditionalInfo = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void LogCoverQuery(const FVector& CoverLocation, bool bSuccess, float Score = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void LogFlankQuery(const FVector& FlankLocation, bool bSuccess, float Score = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void LogReviveQuery(const FVector& ReviveLocation, bool bSuccess, float Score = 0.0f);

    // Visualization functions
    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void ShowHeatmap(bool bShow = true);

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void ClearHeatmap();

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void GeneratePostMatchReport();

    // Data export functions
    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void ExportToCSV(const FString& Filename = TEXT("EQS_Debug_Data"));

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    void ExportToJSON(const FString& Filename = TEXT("EQS_Debug_Data"));

    // Analysis functions
    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    TArray<FEQSDebugEntry> GetEntriesByType(const FString& QueryType);

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    TArray<FEQSDebugEntry> GetEntriesInTimeRange(float StartTime, float EndTime);

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    float GetSuccessRateForQueryType(const FString& QueryType);

    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bEnableDebugLogging = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bEnableRealTimeVisualization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bLogToFile = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    int32 MaxLogEntries = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    FString LogFilePrefix = TEXT("EQS_Debug");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    FEQSHeatmapSettings HeatmapSettings;

    // Real-time debug display
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Real-time Debug")
    bool bShowDebugSpheres = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Real-time Debug")
    bool bShowDebugLines = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Real-time Debug")
    bool bShowDebugText = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Real-time Debug")
    float DebugDisplayTime = 5.0f;

protected:
    // Internal data storage
    UPROPERTY()
    TArray<FEQSDebugEntry> DebugEntries;

    UPROPERTY()
    TArray<class UStaticMeshComponent*> HeatmapSpheres;

    // File I/O
    void WriteToLogFile(const FEQSDebugEntry& Entry);
    FString GetLogFilePath() const;
    FString GetTimestamp() const;

    // Visualization helpers
    void CreateHeatmapSphere(const FVector& Location, const FColor& Color, float Radius);
    void UpdateHeatmapVisualization();
    void CleanupHeatmapSpheres();
    FColor GetColorForQueryType(const FString& QueryType, bool bSuccess) const;

    // Analysis helpers
    void AnalyzeQueryPatterns();
    void GenerateHeatmapData();
    void CalculateStatistics();

    // Internal state
    float MatchStartTime;
    int32 CurrentMatchPhase;
    FString CurrentAIRole;
    FString CurrentTacticState;

public:
    // Static utility functions for global debugging
    UFUNCTION(BlueprintCallable, Category = "EQS Debug", CallInEditor = true)
    static void EnableGlobalEQSDebug(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "EQS Debug", CallInEditor = true)
    static void ClearAllDebugData();

    UFUNCTION(BlueprintCallable, Category = "EQS Debug")
    static UEQSDebuggerComponent* GetDebuggerForAI(AActor* AIActor);

    // Console commands
    UFUNCTION(Exec)
    void EQSDebug_Show();

    UFUNCTION(Exec)
    void EQSDebug_Hide();

    UFUNCTION(Exec)
    void EQSDebug_Clear();

    UFUNCTION(Exec)
    void EQSDebug_Export();

    UFUNCTION(Exec)
    void EQSDebug_Stats();

private:
    // Performance tracking
    int32 TotalQueries;
    int32 SuccessfulQueries;
    float AverageQueryTime;
    
    // File handle for continuous logging
    TSharedPtr<class FArchive> LogFileHandle;
    
    // Cached references
    class ATDMAIController* CachedAIController;
    class UWorld* CachedWorld;
};
