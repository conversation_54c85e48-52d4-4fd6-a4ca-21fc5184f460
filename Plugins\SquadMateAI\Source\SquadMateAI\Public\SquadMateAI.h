#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogSquadMateAI, Log, All);

/**
 * SquadMate AI Plugin Module
 * Provides complete PUBG-style Team Deathmatch AI system
 */
class FSquadMateAIModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /** Check if the module is loaded and ready */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("SquadMateAI");
    }

    /** Get the module instance */
    static inline FSquadMateAIModule& Get()
    {
        return FModuleManager::LoadModuleChecked<FSquadMateAIModule>("SquadMateAI");
    }

private:
    /** Initialize AI systems */
    void InitializeAISystems();

    /** Cleanup AI systems */
    void CleanupAISystems();

    /** Register console commands */
    void RegisterConsoleCommands();

    /** Unregister console commands */
    void UnregisterConsoleCommands();

    /** Console command delegates */
    TArray<class IConsoleCommand*> ConsoleCommands;
};
