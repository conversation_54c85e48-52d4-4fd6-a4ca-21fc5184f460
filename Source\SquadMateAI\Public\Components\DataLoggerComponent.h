#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "TDMAIController.h"
#include "DataLoggerComponent.generated.h"

// Data entry for EQS action logging
USTRUCT(BlueprintType)
struct FEQSActionEntry : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    float Timestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    FVector Location;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    FString ActionType; // "Cover", "Flank", "Revive"

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    bool bSuccess;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    float Score;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    FString AIRole;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    FString TacticState;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    int32 MatchPhase;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    FVector EnemyLocation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    int32 TeamID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS Action")
    float HealthPercentage;

    FEQSActionEntry()
    {
        Timestamp = 0.0f;
        Location = FVector::ZeroVector;
        ActionType = TEXT("Unknown");
        bSuccess = false;
        Score = 0.0f;
        AIRole = TEXT("Unknown");
        TacticState = TEXT("Patrol");
        MatchPhase = 0;
        EnemyLocation = FVector::ZeroVector;
        TeamID = 0;
        HealthPercentage = 100.0f;
    }
};

// Heatmap sphere data for visualization
USTRUCT(BlueprintType)
struct FHeatmapSphereData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    FVector Location;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    FColor Color;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    float Radius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    FString ActionType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heatmap")
    float Intensity; // 0.0 to 1.0

    FHeatmapSphereData()
    {
        Location = FVector::ZeroVector;
        Color = FColor::White;
        Radius = 50.0f;
        ActionType = TEXT("Unknown");
        Intensity = 1.0f;
    }
};

/**
 * Component for logging EQS actions and generating post-match heatmaps
 * Captures cover, flank, and revive decisions for analysis and visualization
 */
UCLASS(BlueprintType, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class SQUADMATEAI_API UDataLoggerComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UDataLoggerComponent();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === LOGGING FUNCTIONS ===
    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void LogEQSAction(const FString& ActionType, const FVector& Location, bool bSuccess, 
                     float Score = 0.0f, const FString& AdditionalInfo = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void LogCoverAction(const FVector& CoverLocation, bool bSuccess, float Score = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void LogFlankAction(const FVector& FlankLocation, bool bSuccess, float Score = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void LogReviveAction(const FVector& ReviveLocation, bool bSuccess, float Score = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void LogKillEvent(const FVector& KillLocation, const FString& WeaponType, float Distance);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void LogDeathEvent(const FVector& DeathLocation, const FString& CauseOfDeath);

    // === VISUALIZATION FUNCTIONS ===
    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void SpawnHeatmapSpheres();

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void ClearHeatmapSpheres();

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void ShowRealtimeDebugSpheres(bool bShow = true);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void GeneratePostMatchHeatmap();

    // === EXPORT FUNCTIONS ===
    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void ExportToCSV(const FString& Filename = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void ExportToJSON(const FString& Filename = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    void SaveHeatmapData(const FString& Filename = TEXT(""));

    // === ANALYSIS FUNCTIONS ===
    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    TArray<FEQSActionEntry> GetActionsByType(const FString& ActionType);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    TArray<FEQSActionEntry> GetActionsInRadius(const FVector& Center, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    float GetSuccessRateForAction(const FString& ActionType);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    TArray<FVector> GetHotspots(const FString& ActionType, int32 MaxHotspots = 10);

    // === CONFIGURATION ===
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging Settings")
    bool bEnableLogging = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging Settings")
    bool bEnableRealtimeVisualization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging Settings")
    bool bAutoExportOnMatchEnd = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging Settings")
    int32 MaxLogEntries = 2000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging Settings")
    FString LogFilePrefix = TEXT("EQS_Match");

    // === VISUALIZATION SETTINGS ===
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bSpawnPersistentSpheres = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    float DefaultSphereRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    float SphereDisplayDuration = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bUseIntensityBasedSize = true;

    // === COLOR CODING ===
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor CoverColor = FColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor FlankColor = FColor::Orange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor ReviveColor = FColor::Purple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor KillColor = FColor::Red;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor DeathColor = FColor::Black;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor SuccessColor = FColor::Green;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Colors")
    FColor FailureColor = FColor::Red;

protected:
    // === INTERNAL DATA ===
    UPROPERTY()
    TArray<FEQSActionEntry> ActionEntries;

    UPROPERTY()
    TArray<FHeatmapSphereData> HeatmapData;

    UPROPERTY()
    TArray<class AActor*> SpawnedHeatmapSpheres;

    // === INTERNAL STATE ===
    float MatchStartTime;
    int32 CurrentMatchPhase;
    FString CurrentMatchID;
    
    // === CACHED REFERENCES ===
    UPROPERTY()
    class ATDMAIController* CachedAIController;

    UPROPERTY()
    class UWorld* CachedWorld;

    // === HELPER FUNCTIONS ===
    void UpdateCurrentState();
    FString GenerateMatchID() const;
    FString GetTimestamp() const;
    FString GetLogDirectory() const;
    FColor GetColorForAction(const FString& ActionType, bool bSuccess) const;
    float CalculateIntensity(const FVector& Location, const FString& ActionType) const;
    
    // === FILE I/O ===
    void WriteToLogFile(const FEQSActionEntry& Entry);
    void CreateLogDirectories();
    
    // === SPHERE MANAGEMENT ===
    void CreateHeatmapSphere(const FHeatmapSphereData& SphereData);
    void UpdateHeatmapSphere(AActor* SphereActor, const FHeatmapSphereData& SphereData);
    void CleanupSpawnedSpheres();
    
    // === ANALYSIS HELPERS ===
    void CalculateHotspots();
    void GenerateHeatmapIntensity();
    TArray<FVector> ClusterLocations(const TArray<FVector>& Locations, float ClusterRadius = 200.0f);

public:
    // === STATIC UTILITY FUNCTIONS ===
    UFUNCTION(BlueprintCallable, Category = "Data Logger", CallInEditor = true)
    static void EnableGlobalLogging(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Data Logger", CallInEditor = true)
    static void ClearAllLogData();

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    static UDataLoggerComponent* GetLoggerForAI(AActor* AIActor);

    UFUNCTION(BlueprintCallable, Category = "Data Logger")
    static void ExportAllAIData(const FString& MatchID = TEXT(""));

    // === CONSOLE COMMANDS ===
    UFUNCTION(Exec)
    void DataLog_Show();

    UFUNCTION(Exec)
    void DataLog_Hide();

    UFUNCTION(Exec)
    void DataLog_Clear();

    UFUNCTION(Exec)
    void DataLog_Export();

    UFUNCTION(Exec)
    void DataLog_Heatmap();

    UFUNCTION(Exec)
    void DataLog_Stats();

private:
    // === PERFORMANCE TRACKING ===
    int32 TotalActions;
    int32 SuccessfulActions;
    float AverageScore;
    
    // === HOTSPOT CACHE ===
    TMap<FString, TArray<FVector>> CachedHotspots;
    float LastHotspotCalculation;
};
