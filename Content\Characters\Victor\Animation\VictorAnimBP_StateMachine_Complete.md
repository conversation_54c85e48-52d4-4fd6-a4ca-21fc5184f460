# 🎥 Victor AnimBP State Machine - Complete Implementation

## 🎯 **State Machine Overview**
Complete animation state machine for Victor AI with tactical combat animations supporting PUBG-style TDM gameplay.

---

## 📋 **Animation Variables Setup**

### **Create these variables in your AnimBP:**

```cpp
// === MOVEMENT VARIABLES ===
bool IsMoving = false;
float MovementSpeed = 0.0f;
float MovementDirection = 0.0f;
FVector Velocity = FVector::ZeroVector;
bool IsInAir = false;
bool IsRunning = false;

// === COMBAT VARIABLES ===
bool IsAiming = false;
bool IsShooting = false;
bool IsReloading = false;
bool HasWeapon = true;
EWeaponType CurrentWeaponType = EWeaponType::AssaultRifle;

// === STANCE VARIABLES ===
bool IsCrouching = false;
bool IsProne = false;
bool IsStanding = true;

// === PEEK VARIABLES ===
bool IsPeekingLeft = false;
bool IsPeekingRight = false;
float PeekAmount = 0.0f; // -1.0 (left) to 1.0 (right)
float LeanAngle = 0.0f;

// === AI STATE VARIABLES ===
ETDMTacticState CurrentTactic = ETDMTacticState::Patrol;
bool IsEngaging = false;
bool IsFlanking = false;
bool IsReviving = false;
bool IsUnderFire = false;

// === AIM OFFSET VARIABLES ===
float AimYaw = 0.0f;
float AimPitch = 0.0f;
FRotator AimRotation = FRotator::ZeroRotator;
```

---

## 🎭 **Main State Machine: "VictorLocomotion"**

### **State 1: Idle**
```
Entry Rule: MovementSpeed <= 10 && IsStanding && !IsAiming
Exit Rules:
├── To Walking: MovementSpeed > 10
├── To Crouching: IsCrouching == true
├── To Combat: IsAiming == true
└── To InAir: IsInAir == true

Animation Assets:
├── Idle_Rifle (HasWeapon && CurrentWeaponType == Rifle)
├── Idle_Pistol (HasWeapon && CurrentWeaponType == Pistol)
└── Idle_Unarmed (!HasWeapon)

Blend Settings:
├── Blend In Time: 0.2s
└── Blend Out Time: 0.2s
```

### **State 2: Walking**
```
Entry Rule: MovementSpeed > 10 && MovementSpeed <= 300 && IsStanding
Exit Rules:
├── To Idle: MovementSpeed <= 10
├── To Running: MovementSpeed > 300
├── To Combat: IsAiming == true
└── To Crouching: IsCrouching == true

Animation Assets:
├── BlendSpace2D: BS_Walk_Rifle
│   ├── X-Axis: MovementSpeed (0-300)
│   └── Y-Axis: MovementDirection (-180 to 180)
├── Animations in BlendSpace:
│   ├── Walk_Forward_Rifle (Speed: 150, Direction: 0)
│   ├── Walk_Backward_Rifle (Speed: 150, Direction: 180)
│   ├── Walk_Left_Rifle (Speed: 150, Direction: -90)
│   └── Walk_Right_Rifle (Speed: 150, Direction: 90)

Blend Settings:
├── Blend In Time: 0.15s
└── Blend Out Time: 0.15s
```

### **State 3: Running**
```
Entry Rule: MovementSpeed > 300 && IsStanding && !IsAiming
Exit Rules:
├── To Walking: MovementSpeed <= 300
├── To Combat: IsAiming == true
└── To Crouching: IsCrouching == true

Animation Assets:
├── BlendSpace2D: BS_Run_Rifle
│   ├── X-Axis: MovementSpeed (300-600)
│   └── Y-Axis: MovementDirection (-180 to 180)
├── Animations in BlendSpace:
│   ├── Run_Forward_Rifle (Speed: 450, Direction: 0)
│   ├── Run_Backward_Rifle (Speed: 300, Direction: 180)
│   ├── Run_Left_Rifle (Speed: 400, Direction: -90)
│   └── Run_Right_Rifle (Speed: 400, Direction: 90)

Blend Settings:
├── Blend In Time: 0.1s
└── Blend Out Time: 0.2s
```

### **State 4: InAir**
```
Entry Rule: IsInAir == true
Exit Rules:
└── To Idle: IsInAir == false && MovementSpeed <= 10

Animation Assets:
├── Jump_Start (First 0.3s of jump)
├── Jump_Loop (While in air)
└── Jump_Land (Landing sequence)

State Machine within InAir:
├── JumpStart → JumpLoop (after 0.3s)
├── JumpLoop → JumpLand (IsInAir == false)
└── JumpLand → Exit (after 0.5s)
```

---

## 🎯 **Combat State Machine: "VictorCombat"**

### **State 1: CombatIdle**
```
Entry Rule: IsAiming == true && MovementSpeed <= 10
Exit Rules:
├── To CombatWalk: MovementSpeed > 10
├── To Firing: IsShooting == true
├── To Reloading: IsReloading == true
└── To Exit: IsAiming == false

Animation Assets:
├── Aim_Rifle_Idle (CurrentWeaponType == Rifle)
├── Aim_Pistol_Idle (CurrentWeaponType == Pistol)
└── Aim_SMG_Idle (CurrentWeaponType == SMG)

Additive Layers:
├── AimOffset_Rifle (Aim Yaw/Pitch adjustment)
└── Breathing_Subtle (Idle breathing while aiming)
```

### **State 2: CombatWalk**
```
Entry Rule: IsAiming == true && MovementSpeed > 10
Exit Rules:
├── To CombatIdle: MovementSpeed <= 10
├── To Firing: IsShooting == true
└── To Exit: IsAiming == false

Animation Assets:
├── BlendSpace2D: BS_AimWalk_Rifle
│   ├── X-Axis: MovementSpeed (0-200)
│   └── Y-Axis: MovementDirection (-180 to 180)
├── Animations:
│   ├── AimWalk_Forward_Rifle
│   ├── AimWalk_Backward_Rifle
│   ├── AimWalk_Left_Rifle
│   └── AimWalk_Right_Rifle

Blend Settings:
├── Upper Body Mask: Applied
└── Lower Body: Blend with locomotion
```

### **State 3: Firing**
```
Entry Rule: IsShooting == true
Exit Rules:
├── To CombatIdle: IsShooting == false && MovementSpeed <= 10
└── To CombatWalk: IsShooting == false && MovementSpeed > 10

Animation Assets:
├── Fire_Rifle_Single (Single shot)
├── Fire_Rifle_Auto (Full auto loop)
├── Fire_Pistol_Single
└── Fire_SMG_Auto

Montage Settings:
├── Slot: UpperBody
├── Blend In: 0.05s
├── Blend Out: 0.1s
└── Loop: Based on weapon type
```

### **State 4: Reloading**
```
Entry Rule: IsReloading == true
Exit Rules:
└── To CombatIdle: IsReloading == false

Animation Assets:
├── Reload_Rifle (2.5s duration)
├── Reload_Pistol (1.8s duration)
└── Reload_SMG (2.0s duration)

Animation Notifies:
├── EjectMag (0.5s)
├── InsertMag (1.5s)
├── CockWeapon (2.0s)
└── ReloadComplete (End)
```

---

## 🤸 **Stance State Machine: "VictorStance"**

### **State 1: Standing**
```
Entry Rule: IsStanding == true
Exit Rules:
├── To Crouching: IsCrouching == true
└── To Prone: IsProne == true

Sub-States:
├── Standing_Idle
├── Standing_Walk
├── Standing_Run
└── Standing_Combat
```

### **State 2: Crouching**
```
Entry Rule: IsCrouching == true
Exit Rules:
├── To Standing: IsCrouching == false
└── To Prone: IsProne == true

Animation Assets:
├── Crouch_Idle_Rifle
├── Crouch_Walk_Rifle (BlendSpace1D)
├── Crouch_Aim_Rifle
└── Crouch_Fire_Rifle

Transition Animations:
├── Stand_To_Crouch (0.5s)
└── Crouch_To_Stand (0.4s)
```

### **State 3: Prone**
```
Entry Rule: IsProne == true
Exit Rules:
└── To Crouching: IsProne == false

Animation Assets:
├── Prone_Idle_Rifle
├── Prone_Crawl_Rifle (BlendSpace1D)
├── Prone_Aim_Rifle
└── Prone_Fire_Rifle

Transition Animations:
├── Crouch_To_Prone (1.0s)
└── Prone_To_Crouch (0.8s)
```

---

## 👁️ **Peek State Machine: "VictorPeek"**

### **State 1: Normal**
```
Entry Rule: !IsPeekingLeft && !IsPeekingRight
Exit Rules:
├── To PeekLeft: IsPeekingLeft == true
└── To PeekRight: IsPeekingRight == true

Animation: Normal stance (no peek)
```

### **State 2: PeekLeft**
```
Entry Rule: IsPeekingLeft == true
Exit Rules:
├── To Normal: IsPeekingLeft == false
└── To PeekRight: IsPeekingRight == true

Animation Assets:
├── PeekLeft_Idle
├── PeekLeft_Aim
└── PeekLeft_Fire

Bone Modifications:
├── Spine_02: Rotation Y += -15°
├── Spine_03: Rotation Y += -10°
└── Head: Rotation Y += -20°
```

### **State 3: PeekRight**
```
Entry Rule: IsPeekingRight == true
Exit Rules:
├── To Normal: IsPeekingRight == false
└── To PeekLeft: IsPeekingLeft == true

Animation Assets:
├── PeekRight_Idle
├── PeekRight_Aim
└── PeekRight_Fire

Bone Modifications:
├── Spine_02: Rotation Y += 15°
├── Spine_03: Rotation Y += 10°
└── Head: Rotation Y += 20°
```

---

## 🎬 **Animation Layers & Slots**

### **Layer 1: Base Locomotion**
```
Slot: DefaultSlot
Weight: 1.0
Mask: Full Body
Purpose: Primary movement animations
```

### **Layer 2: Upper Body Combat**
```
Slot: UpperBody
Weight: 1.0
Mask: Upper Body Only (Spine_01 and above)
Purpose: Aiming, firing, reloading
Blend Mode: Blend Per Bone
```

### **Layer 3: Additive Peek**
```
Slot: Additive
Weight: PeekAmount (0.0 to 1.0)
Mask: Spine and Head bones
Purpose: Peek and lean modifications
Blend Mode: Additive
```

### **Layer 4: Facial & Head**
```
Slot: FacialSlot
Weight: 1.0
Mask: Head and Neck bones
Purpose: Look-at and facial expressions
```

---

## 🎮 **Blueprint Event Graph Implementation**

### **Event: Blueprint Update Animation**
```blueprint
[Event Blueprint Update Animation]
 ↓
[Get Pawn Owner]
 ↓
[Cast to Victor Character]
 ↓
[Sequence: Update All Variables]
 ├── Branch 1: Movement
 │   ├── [Get Velocity] → [Vector Length] → Set MovementSpeed
 │   ├── [Get Movement Direction] → Set MovementDirection
 │   └── [Get Is In Air] → Set IsInAir
 ├── Branch 2: Combat
 │   ├── [Get Is Aiming] → Set IsAiming
 │   ├── [Get Is Shooting] → Set IsShooting
 │   ├── [Get Is Reloading] → Set IsReloading
 │   └── [Get Current Weapon Type] → Set CurrentWeaponType
 ├── Branch 3: Stance
 │   ├── [Get Is Crouching] → Set IsCrouching
 │   ├── [Get Is Prone] → Set IsProne
 │   └── [Calculate IsStanding] → Set IsStanding
 └── Branch 4: AI State
     ├── [Get AI Controller] → [Cast to TDMAIController]
     ├── [Get Blackboard] → [Get Tactic State] → Set CurrentTactic
     ├── [Get BB: IsPeekingLeft] → Set IsPeekingLeft
     └── [Get BB: IsPeekingRight] → Set IsPeekingRight
```

### **Function: Update Aim Offset**
```blueprint
[Function: UpdateAimOffset]
 ↓
[Get Control Rotation] (from AI Controller)
 ↓
[Get Actor Rotation] (from Character)
 ↓
[Delta Rotation] → [Break Rotator]
 ↓
[Clamp Yaw] (-90 to 90) → Set AimYaw
 ↓
[Clamp Pitch] (-45 to 45) → Set AimPitch
```

### **Function: Update Peek Amount**
```blueprint
[Function: UpdatePeekAmount]
 ↓
[Branch: IsPeekingLeft]
 ├── True: [Set PeekAmount = -1.0]
 └── False: [Branch: IsPeekingRight]
     ├── True: [Set PeekAmount = 1.0]
     └── False: [Set PeekAmount = 0.0]
 ↓
[Interp To] (Current → Target, Speed: 5.0) → Set LeanAngle
```

---

## 🔧 **Integration with AI Controller**

### **From C++ AI Controller:**
```cpp
// In TDMAIController::UpdateAnimationState()
if (UAnimInstance* AnimInstance = GetCharacter()->GetMesh()->GetAnimInstance())
{
    // Combat states
    AnimInstance->SetBool("IsAiming", bIsCurrentlyAiming);
    AnimInstance->SetBool("IsShooting", bIsCurrentlyFiring);
    AnimInstance->SetBool("IsReloading", bIsCurrentlyReloading);
    
    // Stance states
    AnimInstance->SetBool("IsCrouching", bShouldCrouch);
    AnimInstance->SetBool("IsProne", bShouldGoProne);
    
    // Peek states
    AnimInstance->SetBool("IsPeekingLeft", bShouldPeekLeft);
    AnimInstance->SetBool("IsPeekingRight", bShouldPeekRight);
    
    // Tactical states
    AnimInstance->SetEnum("CurrentTactic", (uint8)CurrentTacticState);
    AnimInstance->SetBool("IsUnderFire", bIsUnderFire);
}
```

This complete state machine provides **realistic tactical animations** that integrate seamlessly with your PUBG-style AI behavior system!
