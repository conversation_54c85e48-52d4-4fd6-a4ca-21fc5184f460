#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "EnvironmentQuery/EnvQueryTypes.h"
#include "Engine/DataTable.h"
#include "BTTask_FindCover.generated.h"

// Cover search parameters
USTRUCT(BlueprintType)
struct FCoverSearchParams : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float MaxSearchRadius = 1500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float MinSearchRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float MinCoverHeight = 120.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float PreferredCoverHeight = 180.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float MinDistanceFromEnemy = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float MaxDistanceFromEnemy = 1200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    float SquadCohesionRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    bool bRequireLineOfSight = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Search")
    bool bPreferElevatedPositions = true;

    FCoverSearchParams()
    {
        MaxSearchRadius = 1500.0f;
        MinSearchRadius = 200.0f;
        MinCoverHeight = 120.0f;
        PreferredCoverHeight = 180.0f;
        MinDistanceFromEnemy = 300.0f;
        MaxDistanceFromEnemy = 1200.0f;
        SquadCohesionRadius = 500.0f;
        bRequireLineOfSight = false;
        bPreferElevatedPositions = true;
    }
};

/**
 * Behavior Tree task that uses EQS to find optimal cover positions
 * Integrates with PUBG-style TDM tactical AI system
 */
UCLASS(BlueprintType, meta=(DisplayName="Find Cover (EQS)"))
class SQUADMATEAI_API UBTTask_FindCover : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_FindCover();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // EQS Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* CoverQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* EmergencyCoverQuery;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector CoverLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsUnderFireKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector SquadMembersKey;

    // Cover Search Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover Parameters")
    FCoverSearchParams CoverParams;

    // Emergency Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emergency")
    FCoverSearchParams EmergencyParams;

    // Query Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryTimeLimit = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bUseEmergencyQueryWhenUnderFire = true;

    // Movement Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bMoveToFoundCover = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float AcceptableRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bUsePathfinding = true;

private:
    // EQS Query handling
    void StartCoverQuery(UBehaviorTreeComponent& OwnerComp);
    void OnEQSQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp);
    
    // Query selection and configuration
    UEnvQuery* SelectOptimalQuery(UBehaviorTreeComponent& OwnerComp);
    void ConfigureQueryParams(FEnvQueryRequest& QueryRequest, UBehaviorTreeComponent& OwnerComp);
    FCoverSearchParams GetAdjustedParams(UBehaviorTreeComponent& OwnerComp);

    // Utility functions
    bool IsUnderFire(UBehaviorTreeComponent& OwnerComp);
    AActor* GetCurrentTarget(UBehaviorTreeComponent& OwnerComp);
    TArray<AActor*> GetSquadMembers(UBehaviorTreeComponent& OwnerComp);
    
    // Cover evaluation
    bool ValidateCoverPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp);
    float ScoreCoverPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp);

    // Movement handling
    void InitiateMovementToCover(const FVector& CoverLocation, UBehaviorTreeComponent& OwnerComp);

    // Cached references
    TWeakObjectPtr<UBehaviorTreeComponent> CachedOwnerComp;
    FEnvQueryRequest ActiveQueryRequest;
};
