# 📊 DataTable Import Instructions

## 🎯 Overview

I've created 5 complete CSV files that contain all the data for your TDM AI system. These files can be imported directly into Unreal Engine 5 as DataTables.

## 📁 Files Created

### ✅ **DT_WeaponStats.csv**
- **10 PUBGM weapons** with accurate stats
- **Damage, fire rate, accuracy, recoil** data
- **Magazine sizes and reload times**

### ✅ **DT_RoleLoadouts.csv**
- **8 role configurations** (5 primary + 3 alternatives)
- **Weapon assignments per role**
- **Aggression and mobility levels**

### ✅ **DT_AIDecisions.csv**
- **10 AI decision types** with priorities
- **Condition-action pairs** for tactical behavior
- **Role-specific decision making**

### ✅ **DT_MapPositions.csv**
- **21 map positions** for warehouse TDM
- **10 spawn points** (5 per team)
- **Strategic positions and cover points**

### ✅ **DT_TacticalParameters.csv**
- **31 system parameters** for fine-tuning
- **PUBGM movement speeds and ranges**
- **AI difficulty and match settings**

## 🚀 Import Process

### Step 1: Prepare Folder Structure
1. **Create folder**: `Content/Data/DataTables/`
2. **Copy all 5 CSV files** to this folder

### Step 2: Import Each DataTable

#### For Each CSV File:

1. **Right-click the CSV file** in Content Browser
2. **Select "Import to..."**
3. **Choose "Data Table"**
4. **Set Row Structure**: `DataTableRowBase`
5. **Click "Import"**
6. **Rename** to remove `.csv` extension:
   - `DT_WeaponStats.csv` → `DT_WeaponStats`
   - `DT_RoleLoadouts.csv` → `DT_RoleLoadouts`
   - `DT_AIDecisions.csv` → `DT_AIDecisions`
   - `DT_MapPositions.csv` → `DT_MapPositions`
   - `DT_TacticalParameters.csv` → `DT_TacticalParameters`

### Step 3: Verify Import

#### Check Each DataTable:
1. **Double-click** to open DataTable editor
2. **Verify all rows** are imported correctly
3. **Check data types** match expectations
4. **Ensure no missing values**

## 🔧 Alternative Manual Creation

### If CSV Import Doesn't Work:

#### Create Empty DataTables:
1. **Right-click** in Content/Data/DataTables/
2. **Miscellaneous → Data Table**
3. **Row Structure**: `DataTableRowBase`
4. **Name**: `DT_WeaponStats` (etc.)

#### Add Columns Manually:

##### DT_WeaponStats Columns:
- WeaponName (String)
- WeaponType (String)
- Damage (Float)
- FireRate (Float)
- Range (Float)
- Accuracy (Float)
- Stability (Float)
- MagazineSize (Integer)
- ReloadTime (Float)
- RecoilVertical (Float)
- RecoilHorizontal (Float)
- IsAutomatic (Boolean)
- CanADS (Boolean)

##### DT_RoleLoadouts Columns:
- RoleName (String)
- PrimaryWeapon (String)
- SecondaryWeapon (String)
- PreferredRange (String)
- PlayStyle (String)
- LanePreference (String)
- AggressionLevel (Float)
- SupportLevel (Float)
- MobilityLevel (Float)
- HasMelee (Boolean)
- Priority (Integer)

##### DT_AIDecisions Columns:
- DecisionName (String)
- Priority (Integer)
- CooldownTime (Float)
- Conditions (String)
- Actions (String)
- RequiredRole (String)
- MinTeammates (Integer)
- MaxRange (Float)
- MinHealth (Float)
- Description (String)

##### DT_MapPositions Columns:
- PositionName (String)
- PositionType (String)
- LocationX (Float)
- LocationY (Float)
- LocationZ (Float)
- RotationYaw (Float)
- TeamID (Integer)
- LaneType (String)
- Importance (String)
- PreferredRoles (String)
- CoverType (String)
- SightLines (String)
- Radius (Float)
- IsSpawnPoint (Boolean)
- IsObjective (Boolean)

##### DT_TacticalParameters Columns:
- ParameterName (String)
- Category (String)
- ValueType (String)
- FloatValue (Float)
- IntValue (Integer)
- BoolValue (Boolean)
- StringValue (String)
- Description (String)
- MinValue (Float)
- MaxValue (Float)
- DefaultValue (Float)

## 📋 Data Entry Examples

### DT_WeaponStats Sample Rows:

#### AR_M416:
- WeaponName: "M416"
- WeaponType: "AssaultRifle"
- Damage: 43.0
- FireRate: 700.0
- Range: 500.0
- Accuracy: 85.0
- Stability: 80.0
- MagazineSize: 30
- ReloadTime: 2.3
- RecoilVertical: 15.0
- RecoilHorizontal: 8.0
- IsAutomatic: true
- CanADS: true

#### SMG_UMP45:
- WeaponName: "UMP45"
- WeaponType: "SubmachineGun"
- Damage: 35.0
- FireRate: 600.0
- Range: 200.0
- Accuracy: 70.0
- Stability: 90.0
- MagazineSize: 25
- ReloadTime: 2.0
- RecoilVertical: 8.0
- RecoilHorizontal: 10.0
- IsAutomatic: true
- CanADS: true

### DT_RoleLoadouts Sample Rows:

#### Assault:
- RoleName: "Assault"
- PrimaryWeapon: "AR_M416"
- SecondaryWeapon: "SMG_UMP45"
- PreferredRange: "Medium"
- PlayStyle: "Aggressive_Balanced"
- LanePreference: "Center"
- AggressionLevel: 0.7
- SupportLevel: 0.6
- MobilityLevel: 0.6
- HasMelee: true
- Priority: 1

#### Sniper:
- RoleName: "Sniper"
- PrimaryWeapon: "SNIPER_KAR98"
- SecondaryWeapon: "AR_AKM"
- PreferredRange: "Long"
- PlayStyle: "Defensive_Overwatch"
- LanePreference: "Back_Center"
- AggressionLevel: 0.3
- SupportLevel: 0.7
- MobilityLevel: 0.2
- HasMelee: true
- Priority: 3

## 🧪 Testing DataTables

### Verification Steps:
1. **Open each DataTable** in editor
2. **Check row count**:
   - DT_WeaponStats: 10 rows
   - DT_RoleLoadouts: 8 rows
   - DT_AIDecisions: 10 rows
   - DT_MapPositions: 21 rows
   - DT_TacticalParameters: 31 rows
3. **Verify data types** are correct
4. **Test Blueprint access**:

```blueprint
// Test weapon stats access
Get Data Table Row
├── Data Table: DT_WeaponStats
├── Row Name: "AR_M416"
└── Print Damage Value
```

## 🎮 Integration with AI System

### Blueprint Setup:
1. **Create BP_TDMAIController** (if not exists)
2. **Add DataTable references**:
   - WeaponStatsTable → DT_WeaponStats
   - RoleLoadoutsTable → DT_RoleLoadouts
   - AIDecisionsTable → DT_AIDecisions
   - MapPositionsTable → DT_MapPositions
   - TacticalParametersTable → DT_TacticalParameters

### Usage Examples:
```blueprint
// Get weapon damage
Get Data Table Row (DT_WeaponStats, "AR_M416")
└── Use Damage value for combat calculations

// Assign role loadout
Get Data Table Row (DT_RoleLoadouts, "Assault")
└── Set Primary/Secondary weapons

// Make AI decision
Get Data Table Rows (DT_AIDecisions)
└── Evaluate conditions and execute actions

// Get spawn position
Get Data Table Rows (DT_MapPositions)
└── Filter by TeamID and IsSpawnPoint
```

## 🚨 Troubleshooting

### Common Issues:

#### CSV Import Fails:
- **Solution**: Create DataTables manually and copy data
- **Check**: File encoding (should be UTF-8)
- **Verify**: No special characters in data

#### Wrong Data Types:
- **Solution**: Recreate columns with correct types
- **Check**: Boolean values are TRUE/FALSE
- **Verify**: Float values use decimal points

#### Missing Data:
- **Solution**: Copy all rows from CSV files exactly
- **Check**: No empty cells in critical columns
- **Verify**: All required entries are present

#### Blueprint Access Errors:
- **Solution**: Ensure DataTable references are set
- **Check**: Row names match exactly (case-sensitive)
- **Verify**: DataTables are compiled and saved

## ✅ Success Checklist

- [ ] All 5 CSV files created in correct folder
- [ ] All DataTables imported successfully
- [ ] Correct number of rows in each table
- [ ] Data types match specifications
- [ ] Blueprint references configured
- [ ] Test access works in Play mode

Once completed, you'll have a fully functional data-driven AI system with authentic PUBGM mechanics!
