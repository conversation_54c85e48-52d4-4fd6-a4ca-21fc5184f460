# 🧩 BTService_RunDynamicEQS Blueprint Implementation Guide

## 🎯 **Purpose**
Automatically triggers the appropriate EQS query based on the AI's current tactical situation, providing dynamic spatial reasoning for your PUBG-style TDM AI.

---

## 📁 **Step 1: Create the Blueprint Service**

### **Create New Blueprint:**
1. **Right-click in Content/AI/Blueprints/**
2. **Blueprint Class → Search: "BTService"**
3. **Select: BTService_BlueprintBase**
4. **Name: `BTService_RunDynamicEQS`**

### **Configure Service Properties:**
- **Interval**: 2.0 seconds
- **Random Deviation**: 0.5 seconds
- **Call Tick on Search Start**: True
- **Restart Timer on Each Search**: False

---

## ⚙️ **Step 2: Event Graph Implementation**

### **Main Event: Receive Tick AI**

```blueprint
[Event Receive Tick AI]
 ↓
[Cast to TDMAIController] (Owner Controller)
 ↓
[Get Blackboard Component]
 ↓
[Get Blackboard Value as Enum: "TacticState"]
 ↓
[Switch on Enum: TacticState]
 ├── Case: Engage → [Call Function: RunFlankQuery]
 ├── Case: Revive → [Call Function: RunReviveQuery]  
 ├── Case: Hold → [Call Function: RunCoverQuery]
 ├── Case: Retreat → [Call Function: RunEmergencyCover]
 └── Default → [Call Function: RunCoverQuery]
```

### **Function: RunFlankQuery**
```blueprint
[Function: RunFlankQuery]
 ↓
[Get Blackboard Value as Object: "TargetActor"]
 ↓
[Branch: Is Valid?]
 ├── True:
 │   ↓
 │   [Run EQS Query]
 │   ├── Query Template: EQ_FindFlank
 │   ├── Querier: Get Controlled Pawn
 │   ├── Run Mode: Single Result
 │   └── On Query Finished → [Set Blackboard Value: "FlankLocation"]
 └── False:
     ↓
     [Call Function: RunCoverQuery]
```

### **Function: RunCoverQuery**
```blueprint
[Function: RunCoverQuery]
 ↓
[Get Blackboard Value as Bool: "IsUnderFire"]
 ↓
[Branch: IsUnderFire?]
 ├── True:
 │   ↓
 │   [Run EQS Query]
 │   ├── Query Template: EQ_EmergencyCover
 │   ├── Querier: Get Controlled Pawn
 │   ├── Run Mode: Single Result
 │   └── On Query Finished → [Set Blackboard Value: "CoverLocation"]
 └── False:
     ↓
     [Run EQS Query]
     ├── Query Template: EQ_FindCover
     ├── Querier: Get Controlled Pawn
     ├── Run Mode: Single Result
     └── On Query Finished → [Set Blackboard Value: "CoverLocation"]
```

### **Function: RunReviveQuery**
```blueprint
[Function: RunReviveQuery]
 ↓
[Get Blackboard Value as Object: "ReviveTarget"]
 ↓
[Branch: Is Valid?]
 ├── True:
 │   ↓
 │   [Run EQS Query]
 │   ├── Query Template: EQ_ReviveSafeSpot
 │   ├── Querier: Get Controlled Pawn
 │   ├── Run Mode: Single Result
 │   └── On Query Finished → [Set Blackboard Value: "ReviveLocation"]
 └── False:
     ↓
     [Set Blackboard Value as Enum: "TacticState" = Patrol]
```

---

## 🎛️ **Step 3: Advanced Configuration**

### **Role-Based Query Selection:**
```blueprint
[Get Blackboard Value as Enum: "SquadRole"]
 ↓
[Switch on Enum: SquadRole]
 ├── Case: Scout → Use EQ_StealthFlank
 ├── Case: Assault → Use EQ_AggressiveFlank
 ├── Case: Support → Use EQ_SupportCover
 └── Default → Use standard queries
```

### **Situational Modifiers:**
```blueprint
[Sequence: Check Situation]
 ↓
[Get Blackboard Value as Float: "HealthPercentage"]
 ↓
[Branch: Health < 30%?]
 ├── True → Force Emergency Cover Query
 └── False → Continue normal logic
```

### **Team Coordination:**
```blueprint
[Get Blackboard Value as Object Array: "SquadMembers"]
 ↓
[For Each Loop: Squad Members]
 ↓
[Get Distance to Squad Member]
 ↓
[Branch: Distance > 800?]
 ├── True → Prioritize squad cohesion in query
 └── False → Continue normal query
```

---

## 📊 **Step 4: Query Parameter Configuration**

### **Dynamic Parameter Setting:**
```blueprint
[Before Run EQS Query]
 ↓
[Set Float Parameter: "SearchRadius"]
 ├── Under Fire: 600.0
 ├── Normal: 1000.0
 └── Flanking: 1200.0
 ↓
[Set Float Parameter: "MinDistance"]
 ├── Emergency: 100.0
 ├── Normal: 300.0
 └── Flanking: 400.0
 ↓
[Set Actor Parameter: "EnemyTarget"]
 └── Get from Blackboard: "TargetActor"
```

---

## 🔧 **Step 5: Error Handling & Fallbacks**

### **Query Failure Handling:**
```blueprint
[On EQS Query Failed]
 ↓
[Print String: "EQS Query Failed - Using Fallback"]
 ↓
[Switch on Failed Query Type]
 ├── Flank Failed → Try Cover Query
 ├── Cover Failed → Use Last Known Safe Position
 └── Revive Failed → Abort Revive, Switch to Hold
```

### **Timeout Protection:**
```blueprint
[Set Timer by Function Name]
 ├── Function Name: "EQSTimeout"
 ├── Time: 3.0 seconds
 └── Looping: False
 ↓
[Function: EQSTimeout]
 ↓
[Clear Timer by Function Name: "EQSTimeout"]
 ↓
[Force Fallback Behavior]
```

---

## 🎮 **Step 6: Integration with Behavior Tree**

### **Add Service to Behavior Tree:**
1. **Open your main Behavior Tree**
2. **Right-click on Composite Node (Selector/Sequence)**
3. **Add Service → BTService_RunDynamicEQS**
4. **Configure Interval: 2.0 seconds**

### **Behavior Tree Structure:**
```
Root
└── Selector: Main AI Logic
    ├── [Service: BTService_RunDynamicEQS]
    ├── Sequence: Combat Response
    │   ├── Decorator: Has Target
    │   ├── Task: MoveTo (FlankLocation/CoverLocation)
    │   └── Task: Engage Enemy
    ├── Sequence: Revive Ally
    │   ├── Decorator: Has Revive Target
    │   ├── Task: MoveTo (ReviveLocation)
    │   └── Task: Revive Ally
    └── Task: Patrol
```

---

## 🧪 **Step 7: Testing & Debugging**

### **Debug Output:**
```blueprint
[After Each EQS Query]
 ↓
[Print String]
 ├── Text: "EQS Result: {QueryType} at {Location}"
 ├── Duration: 5.0
 └── Color: Green (Success) / Red (Failure)
```

### **Visual Debug:**
```blueprint
[On Query Success]
 ↓
[Draw Debug Sphere]
 ├── Location: Query Result
 ├── Radius: 100.0
 ├── Color: Query Type Color
 └── Duration: 10.0 seconds
```

### **Performance Monitoring:**
```blueprint
[Before EQS Query]
 ↓
[Get Game Time in Seconds] → Store as "QueryStartTime"
 ↓
[After EQS Query]
 ↓
[Get Game Time in Seconds] → Subtract "QueryStartTime"
 ↓
[Print String: "Query took {Time} seconds"]
```

---

## ⚡ **Performance Optimization**

### **Query Caching:**
- **Cache Duration**: 5 seconds for same tactical state
- **Cache Invalidation**: On tactical state change or enemy position change
- **Memory Management**: Clear cache on AI death/respawn

### **Frequency Control:**
- **High Priority**: Under fire (0.5s interval)
- **Normal Priority**: Combat (2.0s interval)  
- **Low Priority**: Patrol (5.0s interval)

This Blueprint service provides **intelligent, dynamic EQS selection** that adapts to your AI's tactical needs in real-time!
