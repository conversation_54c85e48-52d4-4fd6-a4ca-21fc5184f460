---,ParameterName,Category,ValueType,FloatValue,IntValue,BoolValue,StringValue,Description,MinValue,MaxValue,DefaultValue
engagement_range_close,engagement_range_close,Combat,Float,100.0,0,FALSE,,Close quarters engagement range in units,50.0,150.0,100.0
engagement_range_medium,engagement_range_medium,Combat,Float,300.0,0,FALSE,,Medium range engagement distance,200.0,400.0,300.0
engagement_range_long,engagement_range_long,Combat,Float,600.0,0,FALSE,,Long range engagement distance,500.0,800.0,600.0
engagement_range_sniper,engagement_range_sniper,Combat,Float,1000.0,0,FALSE,,Sniper effective range,800.0,1200.0,1000.0
reaction_time_immediate_threat,reaction_time_immediate_threat,AI_Performance,Float,0.1,0,FALSE,,Reaction time for immediate threats,0.05,0.2,0.1
reaction_time_enemy_spotted,reaction_time_enemy_spotted,AI_Performance,Float,0.3,0,FALSE,,Reaction time for enemy detection,0.2,0.5,0.3
reaction_time_tactical_decision,reaction_time_tactical_decision,AI_Performance,Float,0.5,0,FALSE,,Time for tactical decision making,0.3,1.0,0.5
reaction_time_positioning,reaction_time_positioning,AI_Performance,Float,1.0,0,FALSE,,Time for positioning decisions,0.5,2.0,1.0
communication_range,communication_range,Team_Coordination,Float,500.0,0,FALSE,,Range for team communication,300.0,1000.0,500.0
support_distance,support_distance,Team_Coordination,Float,200.0,0,FALSE,,Distance to maintain for team support,100.0,300.0,200.0
flank_coordination_time,flank_coordination_time,Team_Coordination,Float,3.0,0,FALSE,,Time to coordinate flanking maneuvers,2.0,5.0,3.0
revive_priority_distance,revive_priority_distance,Team_Coordination,Float,150.0,0,FALSE,,Maximum distance for revive priority,100.0,200.0,150.0
movement_speed_walk,movement_speed_walk,Movement,Float,150.0,0,FALSE,,Walking speed in units/second,100.0,200.0,150.0
movement_speed_run,movement_speed_run,Movement,Float,300.0,0,FALSE,,Running speed in units/second,250.0,350.0,300.0
movement_speed_crouch,movement_speed_crouch,Movement,Float,100.0,0,FALSE,,Crouching speed in units/second,75.0,125.0,100.0
movement_speed_prone,movement_speed_prone,Movement,Float,50.0,0,FALSE,,Prone movement speed in units/second,25.0,75.0,50.0
movement_speed_slide,movement_speed_slide,Movement,Float,400.0,0,FALSE,,Sliding speed in units/second,350.0,450.0,400.0
peek_duration,peek_duration,Combat,Float,1.5,0,FALSE,,Duration of peek action in seconds,1.0,2.5,1.5
suppression_duration,suppression_duration,Combat,Float,3.0,0,FALSE,,Duration of suppressive fire in seconds,2.0,5.0,3.0
reload_cover_seeking,reload_cover_seeking,Combat,Boolean,0.0,0,TRUE,,Seek cover when reloading,0.0,1.0,1.0
auto_lean,auto_lean,Combat,Boolean,0.0,0,TRUE,,Automatically lean around corners,0.0,1.0,1.0
recoil_compensation,recoil_compensation,Combat,Float,0.8,0,FALSE,,Recoil compensation factor (0.0-1.0),0.0,1.0,0.8
health_threshold_low,health_threshold_low,Survival,Float,0.3,0,FALSE,,Health percentage considered low,0.2,0.5,0.3
health_threshold_critical,health_threshold_critical,Survival,Float,0.15,0,FALSE,,Health percentage considered critical,0.1,0.25,0.15
revive_time,revive_time,Survival,Float,5.0,0,FALSE,,Time required to revive teammate,3.0,8.0,5.0
spawn_protection_time,spawn_protection_time,Survival,Float,3.0,0,FALSE,,Spawn protection duration in seconds,2.0,5.0,3.0
match_kill_limit,match_kill_limit,Match,Integer,0.0,40,FALSE,,Kills required to win match,30.0,50.0,40.0
match_time_limit,match_time_limit,Match,Float,600.0,0,FALSE,,Match time limit in seconds,480.0,900.0,600.0
respawn_delay,respawn_delay,Match,Float,3.0,0,FALSE,,Respawn delay in seconds,1.0,5.0,3.0
ai_difficulty_accuracy,ai_difficulty_accuracy,AI_Difficulty,Float,0.75,0,FALSE,,AI accuracy modifier (0.0-1.0),0.3,1.0,0.75
ai_difficulty_reaction_time,ai_difficulty_reaction_time,AI_Difficulty,Float,1.0,0,FALSE,,AI reaction time modifier (1.0=normal),0.5,2.0,1.0
ai_difficulty_aggression,ai_difficulty_aggression,AI_Difficulty,Float,0.7,0,FALSE,,AI aggression level (0.0-1.0),0.3,1.0,0.7
