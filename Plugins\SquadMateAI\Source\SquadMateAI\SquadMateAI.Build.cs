using UnrealBuildTool;

public class SquadMateAI : ModuleRules
{
    public SquadMateAI(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );

        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "AIModule",
                "GameplayTasks",
                "NavigationSystem",
                "UMG",
                "Slate",
                "SlateCore",
                "InputCore",
                "HeadMountedDisplay",
                "EnhancedInput"
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "AIModule",
                "GameplayTasks",
                "NavigationSystem",
                "Json",
                "JsonUtilities",
                "HTTP",
                "RenderCore",
                "RHI",
                "ApplicationCore"
            }
        );

        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {
                // ... add any modules that your module loads dynamically here ...
            }
        );

        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.Speed;
        }

        // Enable debug symbols for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.Debug)
        {
            bUseUnity = false;
        }
    }
}
