#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "EnvironmentQuery/EnvQueryTypes.h"
#include "Engine/DataTable.h"
#include "TDMAIController.h"
#include "BTTask_FlankEnemy.generated.h"

// Flanking execution parameters
USTRUCT(BlueprintType)
struct FFlankExecutionParams : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    float MovementSpeed = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    float AcceptableRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    float EngagementRange = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    float FlankTimeout = 15.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    bool bUseStealthMovement = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    bool bMaintainCover = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    bool bEngageAfterFlank = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    float PreEngagementDelay = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking Execution")
    bool bCoordinateWithTeam = true;

    FFlankExecutionParams()
    {
        MovementSpeed = 400.0f;
        AcceptableRadius = 150.0f;
        EngagementRange = 800.0f;
        FlankTimeout = 15.0f;
        bUseStealthMovement = false;
        bMaintainCover = true;
        bEngageAfterFlank = true;
        PreEngagementDelay = 0.5f;
        bCoordinateWithTeam = true;
    }
};

// Flanking phases for state management
UENUM(BlueprintType)
enum class EFlankPhase : uint8
{
    Planning        UMETA(DisplayName = "Planning"),
    Moving          UMETA(DisplayName = "Moving"),
    Positioning     UMETA(DisplayName = "Positioning"),
    Engaging        UMETA(DisplayName = "Engaging"),
    Completed       UMETA(DisplayName = "Completed"),
    Failed          UMETA(DisplayName = "Failed")
};

/**
 * Advanced Behavior Tree task for executing flanking maneuvers
 * Combines EQS positioning with tactical movement and engagement
 */
UCLASS(BlueprintType, meta=(DisplayName="Flank Enemy (Advanced)"))
class SQUADMATEAI_API UBTTask_FlankEnemy : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_FlankEnemy();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // EQS Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* FlankQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* StealthFlankQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* AggressiveFlankQuery;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector FlankLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector SquadRoleKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsFlankingKey;

    // Execution Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution Parameters")
    FFlankExecutionParams DefaultParams;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution Parameters")
    FFlankExecutionParams StealthParams;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution Parameters")
    FFlankExecutionParams AggressiveParams;

    // Role-Based Adjustments
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Role Adjustments")
    TMap<ETDMSquadRole, FFlankExecutionParams> RoleBasedParams;

    // Animation & Combat Integration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bUseCrouchMovement = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bUsePeekMechanics = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* FlankMovementMontage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* PeekFireMontage;

    // Team Coordination
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Coordination")
    bool bRequestCoverFire = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Coordination")
    bool bBroadcastFlankIntent = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Coordination")
    float TeamCoordinationRadius = 800.0f;

private:
    // Task memory structure
    struct FBTTask_FlankEnemyMemory
    {
        EFlankPhase CurrentPhase;
        FVector FlankPosition;
        AActor* TargetEnemy;
        float TaskStartTime;
        float PhaseStartTime;
        bool bEQSQueryActive;
        bool bMovementActive;
        bool bEngagementActive;
        FTimerHandle TimeoutTimer;
        FTimerHandle EngagementTimer;
    };

    // Core flanking logic
    void StartFlankingSequence(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void ExecuteFlankQuery(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void OnFlankQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp, uint8* NodeMemory);
    
    // Movement and positioning
    void InitiateFlankMovement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateMovementPhase(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void OnFlankPositionReached(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    
    // Combat engagement
    void InitiateEngagement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void ExecutePeekAndShoot(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void HandleCombatPhase(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    
    // Animation integration
    void PlayFlankAnimation(UBehaviorTreeComponent& OwnerComp, const FFlankExecutionParams& Params);
    void PlayPeekFireAnimation(UBehaviorTreeComponent& OwnerComp);
    void SetCrouchState(UBehaviorTreeComponent& OwnerComp, bool bCrouch);
    
    // Team coordination
    void BroadcastFlankIntent(UBehaviorTreeComponent& OwnerComp, const FVector& FlankPosition);
    void RequestTeamCoverFire(UBehaviorTreeComponent& OwnerComp);
    void NotifyFlankCompletion(UBehaviorTreeComponent& OwnerComp, bool bSuccess);
    
    // Utility functions
    UEnvQuery* SelectOptimalFlankQuery(UBehaviorTreeComponent& OwnerComp);
    FFlankExecutionParams GetAdjustedParams(UBehaviorTreeComponent& OwnerComp);
    bool ValidateFlankPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp);
    bool IsTargetStillValid(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool HasClearShotToTarget(UBehaviorTreeComponent& OwnerComp, const FVector& Position);
    
    // Phase management
    void TransitionToPhase(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EFlankPhase NewPhase);
    void HandlePhaseTimeout(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void CleanupFlankingTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Memory management
    FBTTask_FlankEnemyMemory* GetFlankMemory(uint8* NodeMemory) const;
    void InitializeTaskMemory(uint8* NodeMemory) const;

public:
    // Static utility functions
    UFUNCTION(BlueprintCallable, Category = "Flanking", CallInEditor = true)
    static bool IsValidFlankPosition(const FVector& FlankPos, const FVector& EnemyPos, 
                                   const FVector& EnemyForward, float MinAngle = 45.0f);

    UFUNCTION(BlueprintCallable, Category = "Flanking")
    static float CalculateFlankAdvantage(const FVector& FlankPos, const FVector& EnemyPos, 
                                       const FVector& EnemyForward);

    virtual uint16 GetInstanceMemorySize() const override;
};

// Memory size implementation
inline uint16 UBTTask_FlankEnemy::GetInstanceMemorySize() const
{
    return sizeof(FBTTask_FlankEnemyMemory);
}
