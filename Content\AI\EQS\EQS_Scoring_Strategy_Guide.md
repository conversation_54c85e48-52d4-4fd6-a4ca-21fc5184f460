# 🧠 EQS Scoring Strategy Guide for PUBG-Style TDM AI

## 🎯 **Overview**
This guide provides **realistic tactical scoring** for your EQS queries, ensuring AI makes intelligent decisions like professional PUBG Mobile players.

---

## 🛡️ **A. Cover Query Scoring Strategy**

### **Generator Configuration:**
- **Type**: Points on Circle
- **Radius**: 1500 units
- **Points**: 32
- **Context**: <PERSON><PERSON> (AI Agent)

### **Test Configuration & Weights:**

#### **Test 1: Cover Protection (Weight: 0.35)**
```
Test Type: Trace
Purpose: Must block line of sight from enemies
Configuration:
├── From Context: Known Enemies
├── To Context: Item (Test Point)
├── Trace Channel: Visibility
├── Filter Type: Boolean
└── Scoring: Boolean (Blocked = 1.0, Visible = 0.0)
```

#### **Test 2: Distance to Enemy (Weight: 0.25)**
```
Test Type: Distance
Purpose: Maintain tactical engagement distance
Configuration:
├── From Context: Item
├── To Context: Target Actor (Enemy)
├── Filter Type: Range
├── Min Distance: 300 units
├── Max Distance: 1200 units
└── Scoring: Distance (Further = Better)
```

#### **Test 3: Tactical Angle (Weight: 0.20)**
```
Test Type: Dot Product
Purpose: Prefer positions at 90° to enemy facing
Configuration:
├── Line A: Enemy Forward Vector
├── Line B: Enemy to Item Vector
├── Filter Type: Range
├── Min Value: -0.3 (108°)
├── Max Value: 0.3 (72°)
└── Scoring: Absolute Inverse (Closer to 0 = Better)
```

#### **Test 4: Squad Cohesion (Weight: 0.15)**
```
Test Type: Distance
Purpose: Stay close to squad members
Configuration:
├── From Context: Item
├── To Context: Squad Members
├── Filter Type: Range
├── Min Distance: 200 units
├── Max Distance: 600 units
└── Scoring: Inverse Distance (Closer = Better)
```

#### **Test 5: Pathfinding (Weight: 0.05)**
```
Test Type: Pathfinding
Purpose: Ensure position is reachable
Configuration:
├── Path Mode: Navigation Mesh
├── Filter Type: Boolean
└── Scoring: Boolean (Reachable = 1.0)
```

---

## ⚔️ **B. Flank Query Scoring Strategy**

### **Generator Configuration:**
- **Type**: Points on Circle
- **Radius**: 1000 units
- **Points**: 24
- **Context**: Target Actor (Enemy)

### **Test Configuration & Weights:**

#### **Test 1: Flanking Angle (Weight: 0.40)**
```
Test Type: Dot Product
Purpose: Prefer positions behind/beside enemy
Configuration:
├── Line A: Enemy Forward Vector
├── Line B: Enemy to Item Vector
├── Filter Type: Range
├── Min Value: -1.0 (180°)
├── Max Value: 0.2 (78°)
└── Scoring: Inverse (Lower dot = Better flank)
```

#### **Test 2: Line of Sight (Weight: 0.25)**
```
Test Type: Trace
Purpose: Must have clear shot to enemy
Configuration:
├── From Context: Item
├── To Context: Target Actor
├── Trace Channel: Visibility
├── Filter Type: Boolean
└── Scoring: Boolean (Clear = 1.0, Blocked = 0.0)
```

#### **Test 3: Optimal Distance (Weight: 0.20)**
```
Test Type: Distance
Purpose: Maintain effective engagement range
Configuration:
├── From Context: Item
├── To Context: Target Actor
├── Filter Type: Range
├── Min Distance: 400 units
├── Max Distance: 1000 units
└── Scoring: Inverse Distance (Closer to 700 = Better)
```

#### **Test 4: Cover Availability (Weight: 0.10)**
```
Test Type: Trace
Purpose: Ensure flanking position has nearby cover
Configuration:
├── From Context: Item
├── To Context: Nearby Cover Objects
├── Trace Channel: Visibility
├── Max Distance: 200 units
└── Scoring: Boolean (Has Cover = 1.0)
```

#### **Test 5: Avoid Allies (Weight: 0.05)**
```
Test Type: Distance
Purpose: Don't flank through ally positions
Configuration:
├── From Context: Item
├── To Context: Squad Members
├── Filter Type: Range
├── Min Distance: 300 units
└── Scoring: Distance (Further = Better)
```

---

## 🩹 **C. Revive Safe Spot Scoring Strategy**

### **Generator Configuration:**
- **Type**: Points on Circle
- **Radius**: 400 units
- **Points**: 16
- **Context**: Revive Target (Downed Ally)

### **Test Configuration & Weights:**

#### **Test 1: Enemy Line of Sight Block (Weight: 0.40)**
```
Test Type: Trace
Purpose: Must be hidden from all known enemies
Configuration:
├── From Context: Known Enemies
├── To Context: Item
├── Trace Channel: Visibility
├── Filter Type: Boolean
└── Scoring: Boolean (All Blocked = 1.0)
```

#### **Test 2: Distance to Downed Ally (Weight: 0.30)**
```
Test Type: Distance
Purpose: Stay close for quick revive
Configuration:
├── From Context: Item
├── To Context: Revive Target
├── Filter Type: Range
├── Min Distance: 50 units
├── Max Distance: 200 units
└── Scoring: Inverse Distance (Closer = Better)
```

#### **Test 3: Enemy Proximity Avoidance (Weight: 0.20)**
```
Test Type: Distance
Purpose: Stay away from enemy positions
Configuration:
├── From Context: Item
├── To Context: Known Enemies
├── Filter Type: Range
├── Min Distance: 400 units
├── Max Distance: 1000 units
└── Scoring: Distance (Further = Better)
```

#### **Test 4: Ally Support Proximity (Weight: 0.08)**
```
Test Type: Distance
Purpose: Prefer positions near supporting allies
Configuration:
├── From Context: Item
├── To Context: Squad Members (Alive)
├── Filter Type: Range
├── Max Distance: 500 units
└── Scoring: Inverse Distance (Closer = Better)
```

#### **Test 5: Pathfinding (Weight: 0.02)**
```
Test Type: Pathfinding
Purpose: Ensure position is reachable
Configuration:
├── Path Mode: Navigation Mesh
├── Filter Type: Boolean
└── Scoring: Boolean (Reachable = 1.0)
```

---

## ⚡ **Advanced Scoring Techniques**

### **🎯 Dynamic Weight Adjustment:**

#### **Situational Modifiers:**
```cpp
// In C++ or Blueprint
float CoverWeight = BaseWeight;
if (IsUnderFire) CoverWeight *= 1.5f;
if (HealthLow) CoverWeight *= 1.3f;
if (HasSquadSupport) CoverWeight *= 0.8f;
```

#### **Role-Based Weights:**
```cpp
// Scout Role Adjustments
if (Role == Scout) {
    StealthWeight *= 1.4f;
    DistanceWeight *= 1.2f;
    CoverWeight *= 1.1f;
}

// Assault Role Adjustments  
if (Role == Assault) {
    AggressionWeight *= 1.3f;
    DistanceWeight *= 0.8f;
    FlankAngleWeight *= 1.2f;
}
```

### **🧮 Multi-Criteria Scoring:**

#### **Composite Score Calculation:**
```
Final Score = (Test1 * Weight1) + (Test2 * Weight2) + ... + (TestN * WeightN)

Example Cover Score:
= (CoverProtection * 0.35) + (Distance * 0.25) + (Angle * 0.20) + 
  (Cohesion * 0.15) + (Pathfinding * 0.05)
```

#### **Threshold Filtering:**
```
Minimum Acceptable Scores:
├── Cover Protection: 0.7 (Must have good cover)
├── Pathfinding: 1.0 (Must be reachable)
├── Enemy Distance: 0.3 (Not too close)
└── Overall Score: 0.6 (Minimum tactical value)
```

---

## 🎮 **Practical Implementation Tips**

### **🔧 Query Optimization:**

#### **Performance Settings:**
```
Query Limits:
├── Max Results: 5 positions
├── Max Test Time: 0.1 seconds
├── Update Frequency: 2 seconds
└── Cache Duration: 5 seconds
```

#### **LOD System:**
```
Distance-Based Quality:
├── 0-500 units: Full test suite (all weights)
├── 500-1000 units: Reduced tests (core weights only)
└── 1000+ units: Basic tests (essential only)
```

### **🎯 Contextual Adjustments:**

#### **Map-Specific Tuning:**
```
Urban Maps:
├── Increase cover weight (0.45)
├── Reduce distance requirements
└── Add elevation preference

Open Maps:
├── Increase distance weight (0.35)
├── Add long-range engagement bonus
└── Prioritize elevated positions
```

#### **Game Phase Adjustments:**
```
Early Game (0-2 minutes):
├── Conservative positioning
├── Higher cover requirements
└── Squad cohesion priority

Mid Game (2-8 minutes):
├── Balanced aggression
├── Tactical positioning
└── Objective control

Late Game (8+ minutes):
├── Aggressive positioning
├── Risk-taking allowed
└── Victory condition focus
```

This scoring strategy ensures your AI makes **tactically sound decisions** that feel natural and competitive in PUBG-style TDM scenarios!
